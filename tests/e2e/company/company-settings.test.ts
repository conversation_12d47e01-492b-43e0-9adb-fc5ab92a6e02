import { test, expect } from '../../fixtures/auth.fixture';
import { CompanySettingsWorkflow } from '../../workflows/company-settings.workflow';
import { NetworkInterceptor } from '../../utils/network-interceptor';

interface CompanyTestContext {
  workflow: CompanySettingsWorkflow;
  networkInterceptor: NetworkInterceptor;
  originalSettings?: any;
}

test.describe('Company Settings Functionality', () => {
  test.use({ storageState: 'playwright/.auth/manager.json' });
  
  let testContext: CompanyTestContext;

  test.beforeEach(async ({ page, isolatedTest }) => {
    // Initialize test context with enhanced capabilities
    const workflow = new CompanySettingsWorkflow(page);
    const networkInterceptor = new NetworkInterceptor(page);
    
    // Enable API monitoring
    await networkInterceptor.enableApiLogging();
    
    testContext = {
      workflow,
      networkInterceptor
    };
    
    // Track for cleanup
    isolatedTest.addCleanupTask('resetUserPreferences', true);
  });

  test('should update company settings information', async ({ page }) => {
    try {
      const updatedCompany = {
        description: `Updated Description ${Date.now()}`,
        missionStatement: `Updated Mission ${Date.now()}`
      };

      // Navigate to company settings using semantic approach
      await page.goto('/company-settings');
      
      // Wait for page to load
      await expect(page.getByRole('heading', { name: /company.*settings|settings/i }))
        .toBeVisible({ timeout: 10000 });

      // Find and click edit button using semantic locator
      const editButton = page.getByRole('button', { name: /edit|modify|update/i })
        .or(page.getByTestId('edit-company-btn'));
      
      await expect(editButton).toBeVisible({ timeout: 5000 });
      await editButton.click();

      // Update description using semantic locator
      const descriptionField = page.getByLabel(/description/i)
        .or(page.getByTestId('company-description-input'))
        .or(page.locator('textarea[name*="description"], input[name*="description"]'));
      
      if (await descriptionField.isVisible({ timeout: 3000 })) {
        await descriptionField.clear();
        await descriptionField.fill(updatedCompany.description);
      }

      // Update mission statement
      const missionField = page.getByLabel(/mission.*statement|mission/i)
        .or(page.getByTestId('mission-statement-input'))
        .or(page.locator('textarea[name*="mission"], input[name*="mission"]'));
      
      if (await missionField.isVisible({ timeout: 3000 })) {
        await missionField.clear();
        await missionField.fill(updatedCompany.missionStatement);
      }

      // Save changes using semantic locator
      const saveButton = page.getByRole('button', { name: /save|update|submit/i })
        .or(page.getByTestId('save-company-btn'));
      
      await expect(saveButton).toBeVisible();
      await saveButton.click();

      // Verify save success with web-first assertion
      await expect(page.getByText(/saved|updated.*successfully|changes.*saved/i))
        .toBeVisible({ timeout: 5000 })
        .catch(() => {
          console.log('No explicit success message shown');
        });

      // Verify the updates are reflected in the UI
      await expect(page.getByText(updatedCompany.description))
        .toBeVisible({ timeout: 5000 });
      
    } catch (error) {
      await page.screenshot({ 
        path: `test-results/company-settings-failed-${Date.now()}.png`,
        fullPage: true 
      });
      throw new Error(`Company settings test failed: ${error.message}`);
    }
  });

  test('should validate company description update', async ({ page }) => {
    const newDescription = `Validation Test Description ${Date.now()}`;
    
    // Navigate using semantic approach
    await page.goto('/company-settings');
    await expect(page.getByRole('heading', { name: /company.*settings/i })).toBeVisible({ timeout: 10000 });
    
    // Edit description
    const editButton = page.getByRole('button', { name: /edit/i });
    await editButton.click();
    
    const descriptionField = page.getByLabel(/description/i).or(page.getByTestId('company-description-input'));
    await descriptionField.clear();
    await descriptionField.fill(newDescription);
    
    const saveButton = page.getByRole('button', { name: /save/i });
    await saveButton.click();
    
    // Verify persistence with improved selector
    await page.reload();
    await expect(page.getByText(newDescription)).toBeVisible({ timeout: 5000 });
  });

  test('should validate mission statement update', async ({ page }) => {
    const newMissionStatement = 'Updated mission statement for validation test';
    
    await companyWorkflow.navigateToCompanySettings();
    await companyWorkflow.updateMissionStatement(newMissionStatement);
    
    // Navigate back and verify the change persisted
    await companyWorkflow.navigateToBreadcrumb('Company Settings');
    await expect(page.locator('.text-row__wrapper', { hasText: 'Mission statement:' }).getByText(newMissionStatement)).toBeVisible();
  });

  test('should add and manage bank information', async ({ page }) => {
    await companyWorkflow.navigateToCompanySettings();
    
    const bankName = 'Clearstream Banking S.A. Singapore Branch';
    
    // Add bank
    await companyWorkflow.addBank(bankName);
    
    // Verify bank was added (this would need to be implemented based on UI feedback)
    // The original test waits for API response, so we should verify the UI updates
    await expect(page.locator('text=' + bankName)).toBeVisible({ timeout: 10000 });
  });

  test('should handle multiple bank additions', async ({ page }) => {
    await companyWorkflow.navigateToCompanySettings();
    
    const banks = [
      'Clearstream Banking S.A. Singapore Branch',
      'DBS Bank Ltd',
      'Standard Chartered Bank'
    ];
    
    // Add multiple banks
    for (const bank of banks) {
      await companyWorkflow.addBank(bank);
      await page.waitForTimeout(1000); // Allow time for processing
    }
    
    // Verify all banks are displayed
    for (const bank of banks) {
      await expect(page.locator('text=' + bank)).toBeVisible();
    }
  });

  test('should preserve changes after navigation', async ({ page }) => {
    const testData = {
      description: 'Persistent company description test',
      missionStatement: 'Persistent mission statement test'
    };
    
    await companyWorkflow.navigateToCompanySettings();
    await companyWorkflow.updateCompanySettings(testData);
    
    // Navigate away and back
    await page.goto('/marketplace');
    await companyWorkflow.navigateToCompanySettings();
    
    // Verify changes persisted
    await companyWorkflow.verifyCompanySettingsUpdated(testData);
  });
});