import { test, expect } from '../../fixtures/auth.fixture';
import { waitForPageLoad, takeScreenshot } from '../../utils/test-helpers';

test.describe('Application Smoke Tests @smoke', () => {
  test('should load home page successfully', async ({ page }) => {
    await page.goto('/');
    await waitForPageLoad(page);
    
    // Check basic page elements
    await expect(page).toHaveTitle(/G17Eco/);
    
    // Verify key navigation elements
    const loginButton = page.getByRole('button', { name: 'Log in' });
    const marketplaceButton = page.getByRole('button', { name: 'Marketplace' });
    
    await expect(loginButton).toBeVisible();
    await expect(marketplaceButton).toBeVisible();
    
    // Take screenshot for visual validation
    await takeScreenshot(page, 'home-page-loaded');
  });

  test('should successfully authenticate user', async ({ auth }) => {
    // Quick authentication test - this test needs to actually test auth.login
    await auth.login('user');
    
    // Verify login was successful
    expect(await auth.isLoggedIn()).toBe(true);
    
    // Verify we can access authenticated areas
    const currentUser = await auth.getCurrentUser();
    expect(currentUser).toBeTruthy();
  });

  test('should handle logout correctly', async ({ auth }) => {
    // Login first - this test needs to test login/logout flow
    await auth.login('user');
    expect(await auth.isLoggedIn()).toBe(true);
    
    // Logout
    await auth.logout();
    expect(await auth.isLoggedIn()).toBe(false);
  });
});

test.describe('Authenticated Application Smoke Tests @smoke', () => {
  test.use({ storageState: 'playwright/.auth/user.json' });

  test('should navigate through main sections when authenticated', async ({ page }) => {
    // Already authenticated via storage state
    
    // Test navigation to key sections
    const sections = [
      { name: 'Company Tracker', url: '/company-tracker' },
      { name: 'Marketplace', url: '/marketplace' }
    ];
    
    for (const section of sections) {
      await page.goto(section.url);
      await waitForPageLoad(page);
      
      // Verify page loaded without errors
      const title = await page.title();
      expect(title).toBeTruthy();
      
      // Check no critical error messages
      const errorElements = page.locator('.error, [data-testid="error"], .alert-danger');
      const errorCount = await errorElements.count();
      expect(errorCount).toBe(0);
    }
  });
});