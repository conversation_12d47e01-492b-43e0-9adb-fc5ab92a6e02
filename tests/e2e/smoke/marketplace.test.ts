import { test, expect } from '@playwright/test';

test.describe('Marketplace Tests - No Auth Required', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/marketplace');
  });

  test('should load marketplace page', async ({ page }) => {
    await expect(page).toHaveURL(/\/marketplace/);
    await expect(page.locator('.homepage-title').first()).toHaveText('The G17Eco Marketplace');
  });

  test('should display marketplace categories', async ({ page }) => {
    // Check category cards
    await expect(page.locator('.anchor-card-title').getByText('Reporting Tools')).toBeVisible();
    await expect(page.locator('.anchor-card-title').getByText('Education and Training')).toBeVisible();
    await expect(page.locator('.anchor-card-title').getByText('Consultancy')).toBeVisible();
    await expect(page.locator('.anchor-card-title').getByText('Sustainability Solutions')).toBeVisible();
    await expect(page.locator('.anchor-card-title').getByText('Sustainable Finance')).toBeVisible();
  });

  test('should have working navigation', async ({ page }) => {
    // Test home navigation
    await page.getByRole('link', { name: 'Homepage' }).click();
    await expect(page).toHaveURL('/');
    
    // Navigate back to marketplace
    await page.getByRole('button', { name: 'Marketplace' }).click();
    await expect(page).toHaveURL(/\/marketplace/);
  });
});