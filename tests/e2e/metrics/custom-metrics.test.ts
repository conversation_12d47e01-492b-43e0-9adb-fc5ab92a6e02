import { test, expect } from '../../fixtures/auth.fixture';
import { CustomMetricsWorkflow } from '../../workflows/custom-metrics.workflow';
import { NetworkInterceptor } from '../../utils/network-interceptor';
import { GenerateData } from '../../utils/generate-data';

// Enhanced test context for better organization
interface MetricsTestContext {
  workflow: CustomMetricsWorkflow;
  metricGroup: { name: string; id?: string };
  networkInterceptor: NetworkInterceptor;
  createdQuestions: string[];
}

test.describe('Custom Metrics Functionality', () => {
  test.use({ storageState: 'playwright/.auth/manager.json' });
  
  let testContext: MetricsTestContext;

  test.beforeEach(async ({ page, isolatedTest }) => {
    // Initialize enhanced test context
    const workflow = new CustomMetricsWorkflow(page);
    const networkInterceptor = new NetworkInterceptor(page);
    
    // Enable API monitoring for debugging
    await networkInterceptor.enableApiLogging();
    
    // Create metric group for testing
    const metricGroup = await workflow.createMetricGroup();
    
    testContext = {
      workflow,
      metricGroup,
      networkInterceptor,
      createdQuestions: []
    };
    
    // Track for cleanup
    isolatedTest.addCleanupTask('deleteCreatedSurveys', []);
    isolatedTest.addCleanupTask('clearTestData', true);
  });

  test.afterEach(async ({ page }) => {
    try {
      // Clean up created questions first
      for (const questionTitle of testContext.createdQuestions) {
        try {
          await testContext.workflow.deleteCustomQuestion(questionTitle);
        } catch (error) {
          console.warn(`Failed to delete question ${questionTitle}:`, error.message);
        }
      }
      
      // Clear network interceptors
      await testContext.networkInterceptor.clearAllInterceptors();
      
      // Clean up the metric group
      if (testContext.metricGroup?.name) {
        await testContext.workflow.deleteMetricGroup(testContext.metricGroup.name);
      }
    } catch (error) {
      console.warn('Cleanup failed:', error.message);
    }
  });

  test('should add existing questions to custom metric group', async ({ page }) => {
    try {
      const questionsToAdd = [
        'Explanation of Data',
        'Nature of Ownership and Legal Form'
      ];

      // Navigate to metrics management using semantic approach
      await page.goto('/custom-metrics');
      
      // Find the metric group using semantic locators
      const metricGroupCard = page.getByRole('heading', { name: new RegExp(testContext.metricGroup.name, 'i') })
        .or(page.getByText(testContext.metricGroup.name))
        .locator('..');
      
      await expect(metricGroupCard).toBeVisible({ timeout: 10000 });
      
      // Open metric group using semantic button
      const openGroupButton = metricGroupCard.getByRole('button', { name: /open|view|edit/i })
        .or(metricGroupCard.getByRole('link'));
      
      await openGroupButton.click();
      
      // Add questions using enhanced approach
      for (const questionText of questionsToAdd) {
        // Find add question button
        const addQuestionButton = page.getByRole('button', { name: /add.*question|add.*existing/i })
          .or(page.getByTestId('add-question-btn'));
        
        await expect(addQuestionButton).toBeVisible({ timeout: 5000 });
        await addQuestionButton.click();
        
        // Search for the question
        const searchInput = page.getByLabel(/search|find/i)
          .or(page.getByPlaceholder(/search.*questions/i))
          .or(page.getByTestId('question-search'));
        
        if (await searchInput.isVisible({ timeout: 3000 })) {
          await searchInput.fill(questionText);
          await page.waitForTimeout(1000); // Allow search to complete
        }
        
        // Select the question using semantic locator
        const questionOption = page.getByText(questionText)
          .or(page.getByRole('option', { name: new RegExp(questionText, 'i') }));
        
        await expect(questionOption).toBeVisible({ timeout: 5000 });
        await questionOption.click();
        
        // Confirm addition
        const confirmButton = page.getByRole('button', { name: /add|confirm|save/i });
        if (await confirmButton.isVisible({ timeout: 2000 })) {
          await confirmButton.click();
        }
        
        // Verify question was added using web-first assertion
        await expect(page.getByText(/question.*added|added.*successfully/i))
          .toBeVisible({ timeout: 5000 })
          .catch(() => {
            // Some implementations might not show success message
            console.log('No success message shown for question addition');
          });
      }
      
      // Verify questions are now in the group
      for (const questionText of questionsToAdd) {
        await expect(page.getByText(questionText)).toBeVisible({ timeout: 5000 });
      }
      
    } catch (error) {
      await page.screenshot({ 
        path: `test-results/custom-metrics-failed-${Date.now()}.png`,
        fullPage: true 
      });
      throw new Error(`Custom metrics test failed: ${error.message}`);
    }
  });

  test('should create and add custom question to metric group', async ({ page }) => {
    try {
      // Generate unique question data
      const questionData = {
        title: `Custom Question ${Date.now()}`,
        description: 'Test custom question description',
        type: 'text',
        unit: 'percentage'
      };
      
      testContext.createdQuestions.push(questionData.title);
      
      // Navigate to create custom question using semantic approach
      await page.goto('/custom-metrics');
      
      // Find metric group and open it
      const metricGroupCard = page.getByText(testContext.metricGroup.name).locator('..');
      await expect(metricGroupCard).toBeVisible({ timeout: 10000 });
      await metricGroupCard.getByRole('button', { name: /open|view/i }).click();
      
      // Create new custom question
      const createQuestionButton = page.getByRole('button', { name: /create.*question|new.*question/i })
        .or(page.getByTestId('create-custom-question-btn'));
      
      await expect(createQuestionButton).toBeVisible({ timeout: 5000 });
      await createQuestionButton.click();
      
      // Fill question form using semantic locators
      const titleInput = page.getByLabel(/title|question.*title/i)
        .or(page.getByTestId('question-title-input'));
      await expect(titleInput).toBeVisible();
      await titleInput.fill(questionData.title);
      
      const descriptionInput = page.getByLabel(/description/i)
        .or(page.getByTestId('question-description-input'));
      if (await descriptionInput.isVisible({ timeout: 2000 })) {
        await descriptionInput.fill(questionData.description);
      }
      
      const typeSelect = page.getByLabel(/type|question.*type/i)
        .or(page.getByTestId('question-type-select'));
      if (await typeSelect.isVisible({ timeout: 2000 })) {
        await typeSelect.selectOption(questionData.type);
      }
      
      // Save the custom question
      const saveButton = page.getByRole('button', { name: /save|create|add/i })
        .or(page.getByTestId('save-question-btn'));
      
      await saveButton.click();
      
      // Verify question creation with web-first assertion
      await expect(page.getByText(/question.*created|created.*successfully/i))
        .toBeVisible({ timeout: 5000 });
      
      // Verify question appears in the group
      await expect(page.getByText(questionData.title)).toBeVisible({ timeout: 5000 });
      
    } catch (error) {
      await page.screenshot({ 
        path: `test-results/custom-question-creation-failed-${Date.now()}.png`,
        fullPage: true 
      });
      throw new Error(`Custom question creation failed: ${error.message}`);
    }
  });

  test('should edit custom question successfully', async ({ page }) => {
    // First create a custom question
    const originalQuestion = await metricsWorkflow.createCustomQuestionAndAddToGroup(testMetricGroup.name);

    // Edit the custom question
    const updatedTitle = 'Updated Custom Question Title';
    await metricsWorkflow.editCustomQuestion(originalQuestion.title, updatedTitle);

    // Verify the question was updated
    await metricsWorkflow.verifyCustomQuestionUpdated(updatedTitle);

    // Clean up the custom question
    await metricsWorkflow.deleteCustomQuestion(updatedTitle);
  });

  test('should search and filter questions', async ({ page }) => {
    const questionsToAdd = [
      'Explanation of Data',
      'Nature of Ownership and Legal Form',
      'Environmental Impact Assessment'
    ];

    // Add multiple questions to the group
    await metricsWorkflow.addQuestionsToMetricGroup(testMetricGroup.name, questionsToAdd);

    // Search for specific question
    await metricsWorkflow.searchQuestion('Explanation of Data');

    // Verify only the searched question is visible using web-first assertions
    await expect(page.getByText('Explanation of Data')).toBeVisible({ timeout: 5000 });
    await expect(page.getByText('Nature of Ownership and Legal Form')).not.toBeVisible({ timeout: 3000 });
  });

  test('should remove questions from metric group', async ({ page }) => {
    const questionsToAdd = [
      'Explanation of Data',
      'Nature of Ownership and Legal Form'
    ];

    // Add questions to the group
    await metricsWorkflow.addQuestionsToMetricGroup(testMetricGroup.name, questionsToAdd);

    // Remove one question
    await metricsWorkflow.removeQuestionFromGroup('Explanation of Data');

    // Verify the question was removed
    await metricsWorkflow.verifyQuestionRemovedFromGroup('Explanation of Data');

    // Verify the other question is still present
    await expect(page.getByText('Nature of Ownership and Legal Form')).toBeVisible({ timeout: 5000 });
  });

  test('should validate custom question form', async ({ page }) => {
    try {
      // Navigate to custom metrics and open group
      await page.goto('/custom-metrics');
      
      const metricGroupCard = page.getByText(testContext.metricGroup.name).locator('..');
      await expect(metricGroupCard).toBeVisible({ timeout: 10000 });
      await metricGroupCard.getByRole('button', { name: /open|view/i }).click();
      
      // Navigate to create custom question
      const createQuestionButton = page.getByRole('button', { name: /create.*question|new.*question/i });
      await expect(createQuestionButton).toBeVisible({ timeout: 5000 });
      await createQuestionButton.click();

      // Try to submit empty form using semantic locator
      const submitButton = page.getByRole('button', { name: /save|create|submit/i })
        .or(page.getByTestId('custom-question-submit-btn'));
      
      await expect(submitButton).toBeVisible();
      await submitButton.click();

      // Should show validation errors using web-first assertions
      const validationError = page.getByText(/required|please fill|cannot be empty/i)
        .or(page.getByRole('alert'))
        .or(page.locator('.error, .validation-error'));
      
      await expect(validationError).toBeVisible({ timeout: 5000 });
      
    } catch (error) {
      await page.screenshot({ 
        path: `test-results/form-validation-failed-${Date.now()}.png`,
        fullPage: true 
      });
      throw new Error(`Form validation test failed: ${error.message}`);
    }
  });

  test('should handle multiple custom questions creation', async ({ page }) => {
    const customQuestions = [];

    // Create multiple custom questions
    for (let i = 1; i <= 3; i++) {
      const question = await metricsWorkflow.createCustomQuestionAndAddToGroup(
        testMetricGroup.name,
        { title: `Custom Question ${i}` }
      );
      customQuestions.push(question);
    }

    // Verify all questions are in the group
    for (const question of customQuestions) {
      await metricsWorkflow.verifyCustomQuestionAddedToGroup(question);
    }

    // Clean up custom questions
    for (const question of customQuestions) {
      await metricsWorkflow.deleteCustomQuestion(question.title);
    }
  });

  test('should edit and update custom question details', async ({ page }) => {
    // Create a custom question with specific details
    const originalQuestion = await metricsWorkflow.createCustomQuestionAndAddToGroup(
      testMetricGroup.name,
      {
        title: 'Original Question Title',
        description: 'Original description'
      }
    );

    // Edit multiple properties of the question
    const updatedQuestion = {
      title: 'Updated Question Title',
      description: 'Updated description with more details'
    };

    await metricsWorkflow.editCustomQuestion(originalQuestion.title, updatedQuestion.title);

    // Verify the updates
    await metricsWorkflow.verifyCustomQuestionUpdated(updatedQuestion.title);

    // Clean up
    await metricsWorkflow.deleteCustomQuestion(updatedQuestion.title);
  });

  test('should maintain question order in metric group', async ({ page }) => {
    const questionsInOrder = [
      'First Question - Explanation of Data',
      'Second Question - Nature of Ownership',
      'Third Question - Environmental Impact'
    ];

    // Add questions in specific order
    for (const question of questionsInOrder) {
      await metricsWorkflow.addQuestionsToMetricGroup(testMetricGroup.name, [question]);
    }

    // Verify questions appear in the same order
    await metricsWorkflow.verifyQuestionsOrder(questionsInOrder);
  });
});