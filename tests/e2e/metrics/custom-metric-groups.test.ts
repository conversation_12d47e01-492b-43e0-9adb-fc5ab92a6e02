import { test, expect } from '../../fixtures/auth.fixture';
import { CustomMetricsWorkflow } from '../../workflows/custom-metrics.workflow';

test.describe('Custom Metric Groups Functionality', () => {
  test.use({ storageState: 'playwright/.auth/manager.json' });
  
  let metricsWorkflow: CustomMetricsWorkflow;

  test.beforeEach(async ({ page }) => {
    // Already authenticated via storage state
    metricsWorkflow = new CustomMetricsWorkflow(page);
  });

  test('should create a new custom metric group', async ({ page }) => {
    // Navigate to create metric group page and create new group
    const metricGroupData = await metricsWorkflow.createMetricGroup();
    
    // Verify the metric group was created successfully
    await metricsWorkflow.verifyMetricGroupCreated(metricGroupData);
    
    // Clean up - delete the created metric group
    await metricsWorkflow.deleteMetricGroup(metricGroupData.name);
  });

  test('should duplicate an existing metric group', async ({ page }) => {
    // First create a metric group to duplicate
    const originalGroup = await metricsWorkflow.createMetricGroup();
    
    // Navigate to dashboard and duplicate the group
    const duplicateGroupName = await metricsWorkflow.duplicateMetricGroup(originalGroup.name);
    
    // Verify the duplicate was created successfully
    await metricsWorkflow.verifyMetricGroupExists(duplicateGroupName);
    
    // Clean up - delete both groups
    await metricsWorkflow.deleteMetricGroup(originalGroup.name);
    await metricsWorkflow.deleteMetricGroup(duplicateGroupName);
  });

  test('should validate metric group form fields', async ({ page }) => {
    // Navigate to create metric group page
    await metricsWorkflow.navigateToCreateMetricGroup();
    
    // Try to submit empty form
    await page.getByTestId('metric-group-submit-btn').click();
    
    // Should show validation errors
    await expect(page.locator('text=/required|Please fill/i')).toBeVisible();
  });

  test('should handle metric group with long name', async ({ page }) => {
    // Create group with long name
    const longName = 'A'.repeat(100);
    const metricGroupData = await metricsWorkflow.createMetricGroup({
      customName: longName
    });
    
    // Verify it was created and displays properly
    await metricsWorkflow.verifyMetricGroupCreated(metricGroupData);
    
    // Clean up
    await metricsWorkflow.deleteMetricGroup(metricGroupData.name);
  });

  test('should prevent duplicate metric group names', async ({ page }) => {
    // Create first metric group
    const firstGroup = await metricsWorkflow.createMetricGroup();
    
    // Try to create another with same name
    await metricsWorkflow.navigateToCreateMetricGroup();
    
    await page.getByTestId('metric-group-name-input').fill(firstGroup.name);
    await page.getByTestId('metric-group-submit-btn').click();
    
    // Should show error about duplicate name
    await expect(page.locator('text=/already exists|duplicate/i')).toBeVisible();
    
    // Clean up
    await metricsWorkflow.deleteMetricGroup(firstGroup.name);
  });

  test('should navigate between metric group pages', async ({ page }) => {
    // Create a metric group first
    const metricGroupData = await metricsWorkflow.createMetricGroup();
    
    // Navigate to dashboard
    await metricsWorkflow.navigateToMetricGroupDashboard();
    
    // Verify the group appears in the list
    await expect(page.locator('.card-grid-item', { hasText: metricGroupData.name })).toBeVisible();
    
    // Click on the group to view details
    await page.locator('.card-grid-item', { hasText: metricGroupData.name }).click();
    
    // Should navigate to the group details page
    await expect(page.locator('h1', { hasText: metricGroupData.name })).toBeVisible();
    
    // Clean up
    await metricsWorkflow.deleteMetricGroup(metricGroupData.name);
  });

  test('should display metric group cards with correct information', async ({ page }) => {
    // Create multiple metric groups
    const groups = await Promise.all([
      metricsWorkflow.createMetricGroup(),
      metricsWorkflow.createMetricGroup(),
      metricsWorkflow.createMetricGroup()
    ]);
    
    // Navigate to dashboard
    await metricsWorkflow.navigateToMetricGroupDashboard();
    
    // Verify all groups are displayed
    for (const group of groups) {
      await expect(page.locator('.card-grid-item', { hasText: group.name })).toBeVisible();
    }
    
    // Clean up all groups
    for (const group of groups) {
      await metricsWorkflow.deleteMetricGroup(group.name);
    }
  });
});