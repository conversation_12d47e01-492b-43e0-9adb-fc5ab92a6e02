import { test, expect } from '../../fixtures/auth.fixture';

test.describe('SGX Registration Functionality', () => {
  test.describe('SGX Listco Registration', () => {
    test('should complete SGX registration flow with valid company data', async ({ page }) => {
      // Navigate to SGX registration page
      await page.goto('/sgx-esgenome/onboarding');

      // Step 1: Fill in SGX referral code and select company
      const referralCode = 'SGX4SGX';
      const companyName = 'ZHENENG JINJIANG ENVIRONMENT HOLDING COMPANY LIMITED';
      
      await page.locator('#referralCode').fill(referralCode);
      await page.locator('#referralCode').blur();
      
      await page.click('#companyNameSelect');
      await page.getByText(companyName, { exact: true }).click();
      await page.getByTestId('sgx-company-info-next-btn').click();

      // Step 2: Select industry
      await page.waitForSelector('.title-container .h1');
      await expect(page.getByText('Sector info')).toBeVisible();
      
      await page.click('#industrySelect');
      await page.getByText('Airlines', { exact: true }).click();
      await page.getByTestId('sgx-sector-info-next-btn').click();

      // Step 3: Select currency
      await page.click('#currencySelect');
      await page.getByText('Hong Kong Dollar', { exact: true }).click();
      await page.getByTestId('sgx-currency-next-btn').click();

      // Step 4: Verify user email form is displayed
      await page.locator('input[name="email"]').waitFor();
      await expect(page.locator('input[name="email"]')).toBeVisible();
    });

    test('should handle different industry and currency combinations', async ({ page }) => {
      // Navigate to SGX registration page
      await page.goto('/sgx-esgenome/onboarding');

      // Fill in referral code and company
      await page.locator('#referralCode').fill('SGX4SGX');
      await page.locator('#referralCode').blur();
      
      await page.click('#companyNameSelect');
      await page.getByText('ZHENENG JINJIANG ENVIRONMENT HOLDING COMPANY LIMITED', { exact: true }).click();
      await page.getByTestId('sgx-company-info-next-btn').click();

      // Try different industry selection
      await page.waitForSelector('.title-container .h1');
      await expect(page.getByText('Sector info')).toBeVisible();
      
      await page.click('#industrySelect');
      // Verify the dropdown is open and options are available
      await expect(page.getByText('Airlines', { exact: true })).toBeVisible();
      await page.getByText('Airlines', { exact: true }).click();
      await page.getByTestId('sgx-sector-info-next-btn').click();

      // Verify currency selection
      await page.click('#currencySelect');
      // Verify currency options are available
      await expect(page.getByText('Hong Kong Dollar', { exact: true })).toBeVisible();
      await page.getByText('Hong Kong Dollar', { exact: true }).click();
      await page.getByTestId('sgx-currency-next-btn').click();

      // Verify progression to email form
      await page.locator('input[name="email"]').waitFor();
      await expect(page.locator('input[name="email"]')).toBeVisible();
    });

    test('should validate required fields in SGX registration', async ({ page }) => {
      // Navigate to SGX registration page
      await page.goto('/sgx-esgenome/onboarding');

      // Try to proceed without filling referral code
      await page.getByTestId('sgx-company-info-next-btn').click();
      
      // Should show validation error or remain on same step
      // Adjust this based on actual validation behavior
      await expect(page.locator('#referralCode')).toBeVisible();
    });

    test('should handle invalid referral code', async ({ page }) => {
      // Navigate to SGX registration page
      await page.goto('/sgx-esgenome/onboarding');

      // Fill in invalid referral code
      await page.locator('#referralCode').fill('INVALID_CODE');
      await page.locator('#referralCode').blur();
      
      // Try to proceed - should show error or disable company selection
      // Adjust based on actual validation behavior
      const companySelect = page.locator('#companyNameSelect');
      
      // Wait a moment for validation to trigger
      await page.waitForTimeout(1000);
      
      // Verify appropriate error handling
      // This test may need adjustment based on actual validation behavior
    });
  });

  test.describe('SGX Registration Navigation', () => {
    test('should allow navigation between registration steps', async ({ page }) => {
      // Navigate to SGX registration page
      await page.goto('/sgx-esgenome/onboarding');

      // Complete first step
      await page.locator('#referralCode').fill('SGX4SGX');
      await page.locator('#referralCode').blur();
      await page.click('#companyNameSelect');
      await page.getByText('ZHENENG JINJIANG ENVIRONMENT HOLDING COMPANY LIMITED', { exact: true }).click();
      await page.getByTestId('sgx-company-info-next-btn').click();

      // Complete second step
      await page.waitForSelector('.title-container .h1');
      await page.click('#industrySelect');
      await page.getByText('Airlines', { exact: true }).click();
      await page.getByTestId('sgx-sector-info-next-btn').click();

      // Complete third step
      await page.click('#currencySelect');
      await page.getByText('Hong Kong Dollar', { exact: true }).click();
      await page.getByTestId('sgx-currency-next-btn').click();

      // Verify final step
      await expect(page.locator('input[name="email"]')).toBeVisible();
    });
  });
});