import { test, expect } from '../../fixtures/auth.fixture';
import { SurveyWorkflow } from '../../workflows/survey.workflow';
import { testUsers } from '../../fixtures/test-data';

test.describe('Survey Management', () => {
  test.use({ storageState: 'playwright/.auth/user.json' });
  
  let surveyWorkflow: SurveyWorkflow;
  const testSurveyName = `Test Survey ${Date.now()}`;

  test.beforeEach(async ({ page }) => {
    // Already authenticated via storage state
    surveyWorkflow = new SurveyWorkflow(page);
  });

  test('should create a new survey', async () => {
    const surveyData = {
      name: testSurveyName,
      description: 'This is a test survey for integration testing',
      template: 'Basic Survey'
    };

    await surveyWorkflow
      .createSurvey(surveyData)
      .then(workflow => workflow.verifySurveyExists(testSurveyName))
      .then(workflow => workflow.verifySurveyStatus(testSurveyName, 'Draft'));
  });

  test('should add questions to survey', async () => {
    // First create a survey
    await surveyWorkflow.createSurvey({
      name: testSurveyName,
      description: 'Survey for question testing'
    });

    // Open the survey for editing
    await surveyWorkflow.openSurvey(testSurveyName);

    // Add different types of questions
    await surveyWorkflow
      .addQuestion({
        type: 'text',
        text: 'What is your name?',
        required: true
      })
      .then(workflow => workflow.addQuestion({
        type: 'multiple-choice',
        text: 'What is your favorite color?',
        options: ['Red', 'Blue', 'Green', 'Yellow'],
        required: false
      }))
      .then(workflow => workflow.addQuestion({
        type: 'rating',
        text: 'How satisfied are you with our service?',
        required: true
      }));
  });

  test('should publish and fill survey', async () => {
    // Create survey with questions
    await surveyWorkflow
      .createSurvey({
        name: testSurveyName,
        description: 'Survey for testing publication and responses'
      })
      .then(workflow => workflow.openSurvey(testSurveyName))
      .then(workflow => workflow.addQuestion({
        type: 'text',
        text: 'Please provide your feedback',
        required: true
      }))
      .then(workflow => workflow.publishSurvey());

    // Verify survey is published
    await surveyWorkflow.verifySurveyStatus(testSurveyName, 'Published');

    // Fill out the survey as a respondent
    await surveyWorkflow
      .fillSurvey({
        'Please provide your feedback': 'This is my test feedback'
      })
      .then(workflow => workflow.submitSurvey());

    // Verify response was recorded
    const responseCount = await surveyWorkflow.getResponseCount(testSurveyName);
    expect(responseCount).toBeGreaterThan(0);
  });

  test('should view survey responses', async () => {
    // Assuming we have a published survey with responses
    await surveyWorkflow
      .openSurvey(testSurveyName)
      .then(workflow => workflow.viewResponses());

    // Verify responses page loads with data
    await expect(surveyWorkflow.page.locator('[data-testid="survey-responses"]')).toBeVisible();
  });

  test('should export survey data', async () => {
    await surveyWorkflow
      .openSurvey(testSurveyName)
      .then(workflow => workflow.exportSurveyData('csv'));

    // Verify export completed
    await expect(surveyWorkflow.page.locator('text="Export completed"')).toBeVisible();
  });

  test.afterEach(async () => {
    // Clean up: delete test survey
    try {
      await surveyWorkflow.deleteSurvey(testSurveyName);
    } catch {
      // Survey might not exist or already deleted
    }
  });
});

test.describe('Survey Workflow Integration @smoke', () => {
  test.use({ storageState: 'playwright/.auth/manager.json' });
  
  test('critical: end-to-end survey workflow', async ({ page }) => {
    // Already authenticated via storage state
    const surveyWorkflow = new SurveyWorkflow(page);
    const surveyName = `E2E Test Survey ${Date.now()}`;

    // Complete workflow: create → add questions → publish → respond → view results
    await surveyWorkflow
      .createSurvey({
        name: surveyName,
        description: 'End-to-end test survey'
      })
      .then(workflow => workflow.openSurvey(surveyName))
      .then(workflow => workflow.addQuestion({
        type: 'text',
        text: 'What is your overall impression?',
        required: true
      }))
      .then(workflow => workflow.addQuestion({
        type: 'multiple-choice',
        text: 'How did you hear about us?',
        options: ['Search Engine', 'Social Media', 'Friend', 'Other']
      }))
      .then(workflow => workflow.publishSurvey())
      .then(workflow => workflow.fillSurvey({
        'What is your overall impression?': 'Very positive experience',
        'How did you hear about us?': ['Friend']
      }))
      .then(workflow => workflow.submitSurvey())
      .then(workflow => workflow.viewResponses());

    // Verify the complete workflow worked
    const responseCount = await surveyWorkflow.getResponseCount(surveyName);
    expect(responseCount).toBe(1);

    // Clean up
    await surveyWorkflow.deleteSurvey(surveyName);
  });
});