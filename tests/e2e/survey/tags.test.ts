import { test, expect } from '../../fixtures/auth.fixture';
import { SurveyTagsWorkflow } from '../../workflows/survey-tags.workflow';
import { NetworkInterceptor } from '../../utils/network-interceptor';
import { GenerateData } from '../../utils/generate-data';

// Enhanced test context for better type safety
interface TagsTestContext {
  workflow: SurveyTagsWorkflow;
  survey: { id: string; name: string };
  networkInterceptor: NetworkInterceptor;
  createdTags: string[];
}

test.describe('Survey Tags Functionality', () => {
  test.use({ storageState: 'playwright/.auth/manager.json' });
  
  let testContext: TagsTestContext;

  test.beforeEach(async ({ page, isolatedTest }) => {
    // Initialize enhanced test context
    const workflow = new SurveyTagsWorkflow(page);
    const networkInterceptor = new NetworkInterceptor(page);
    
    // Enable API logging for debugging
    await networkInterceptor.enableApiLogging();
    
    // Create survey for testing
    const survey = await workflow.createSurveyWithScope(['Zero Hunger']);
    
    testContext = {
      workflow,
      survey,
      networkInterceptor,
      createdTags: []
    };
    
    // Track for cleanup
    isolatedTest.addCleanupTask('deleteCreatedSurveys', [survey.id]);
    isolatedTest.addCleanupTask('clearTestData', true);
  });

  test.afterEach(async ({ page }) => {
    try {
      // Clean up created tags first
      for (const tagName of testContext.createdTags) {
        try {
          await testContext.workflow.deleteTagFromList(tagName);
        } catch (error) {
          console.warn(`Failed to delete tag ${tagName}:`, error.message);
        }
      }
      
      // Clear network interceptors
      await testContext.networkInterceptor.clearAllInterceptors();
      
      // Clean up the test survey
      if (testContext.survey?.id) {
        await testContext.workflow.deleteSurvey(testContext.survey.id);
      }
    } catch (error) {
      console.warn('Cleanup failed:', error.message);
    }
  });

  test('should create, add, remove, and delete tags', async ({ page }) => {
    try {
      // Navigate to survey overview using enhanced navigation
      await page.goto('/surveys');
      
      // Find and navigate to the survey using semantic locators
      const surveyLink = page.getByRole('link', { name: new RegExp(testContext.survey.name, 'i') })
        .or(page.getByText(testContext.survey.name));
      
      await expect(surveyLink).toBeVisible({ timeout: 10000 });
      await surveyLink.click();
      
      // Wait for survey page to load
      await expect(page).toHaveURL(new RegExp(testContext.survey.id));
      await expect(page.getByRole('main')).toBeVisible();
      
      // Step 1: Create a new tag using semantic locators
      const tagName = `TestTag_${Date.now()}`;
      testContext.createdTags.push(tagName);
      
      // Find tag creation button using semantic approach
      const createTagButton = page.getByRole('button', { name: /create.*tag|new.*tag|add.*tag/i })
        .or(page.getByTestId('create-tag-btn'));
      
      await expect(createTagButton).toBeVisible({ timeout: 5000 });
      await createTagButton.click();
      
      // Fill tag name using semantic locator
      const tagNameInput = page.getByLabel(/tag.*name|name/i)
        .or(page.getByPlaceholder(/enter.*tag.*name/i))
        .or(page.getByTestId('tag-name-input'));
      
      await expect(tagNameInput).toBeVisible();
      await tagNameInput.fill(tagName);
      
      // Save tag using semantic locator
      const saveTagButton = page.getByRole('button', { name: /save|create|add/i })
        .or(page.getByTestId('save-tag-btn'));
      
      await saveTagButton.click();
      
      // Verify tag creation with web-first assertion
      await expect(page.getByText(/tag.*created|success/i)).toBeVisible({ timeout: 5000 });
      
      // Step 2: Add tag to question using semantic approach
      // Find first question row
      const questionRow = page.getByRole('listitem').first()
        .or(page.locator('.trackingListRow, .question-row').first());
      
      await expect(questionRow).toBeVisible({ timeout: 5000 });
      
      // Find add tag button on the question
      const addTagButton = questionRow.getByRole('button', { name: /tag|add.*tag/i })
        .or(questionRow.locator('[data-testid*="tag"]').first());
      
      await addTagButton.click();
      
      // Select the created tag from dropdown or list
      const tagOption = page.getByText(tagName).or(page.getByRole('option', { name: tagName }));
      await expect(tagOption).toBeVisible({ timeout: 3000 });
      await tagOption.click();
      
      // Verify tag was added to question
      await expect(questionRow.getByText(tagName)).toBeVisible({ timeout: 3000 });
      
      // Step 3: Remove tag from question
      const removeTagButton = questionRow.getByRole('button', { name: new RegExp(tagName, 'i') })
        .or(questionRow.getByText(tagName))
        .locator('..') // parent element
        .getByRole('button', { name: /remove|delete|×/i });
      
      if (await removeTagButton.isVisible({ timeout: 2000 })) {
        await removeTagButton.click();
        // Verify tag removed from question
        await expect(questionRow.getByText(tagName)).not.toBeVisible({ timeout: 3000 });
      }
      
      // Step 4: Delete tag from list using improved approach
      const tagsListButton = page.getByRole('button', { name: /tags.*list|manage.*tags/i })
        .or(page.getByTestId('tags-list-btn'));
      
      if (await tagsListButton.isVisible({ timeout: 3000 })) {
        await tagsListButton.click();
        
        // Find and delete the tag
        const tagInList = page.getByText(tagName);
        await expect(tagInList).toBeVisible({ timeout: 5000 });
        
        const deleteButton = tagInList.locator('..')
          .getByRole('button', { name: /delete|remove|×/i });
        
        await deleteButton.click();
        
        // Confirm deletion if dialog appears
        const confirmButton = page.getByRole('button', { name: /confirm|yes|delete/i });
        if (await confirmButton.isVisible({ timeout: 2000 })) {
          await confirmButton.click();
        }
        
        // Verify tag deleted from list
        await expect(page.getByText(tagName)).not.toBeVisible({ timeout: 3000 });
      }
      
    } catch (error) {
      // Enhanced error handling with debugging
      await page.screenshot({ 
        path: `test-results/tags-test-failed-${Date.now()}.png`,
        fullPage: true 
      });
      throw new Error(`Tags test failed: ${error.message}`);
    }
  });

  test('should handle multiple tags on a single question', async ({ page }) => {
    await tagsWorkflow.navigateToSurveyOverview(testSurvey.id);

    const tagNames = [];
    
    // Create multiple tags
    for (let i = 1; i <= 3; i++) {
      const tagName = await tagsWorkflow.createNewTag(`Test Tag ${i}`);
      tagNames.push(tagName);
      await tagsWorkflow.verifyTagCreatedSuccessfully(tagName);
    }

    // Add all tags to a question
    for (const tagName of tagNames) {
      await tagsWorkflow.addTagToQuestion(tagName);
      await tagsWorkflow.verifyTagAddedToQuestion(tagName);
    }

    // Verify all tags are visible on the question
    await tagsWorkflow.verifyMultipleTagsOnQuestion(tagNames);

    // Remove all tags
    for (const tagName of tagNames) {
      await tagsWorkflow.removeTagFromQuestion(tagName);
      await tagsWorkflow.verifyTagRemovedFromQuestion(tagName);
    }

    // Clean up tags
    for (const tagName of tagNames) {
      await tagsWorkflow.deleteTagFromList(tagName);
    }
  });

  test('should prevent duplicate tag names', async ({ page }) => {
    await testContext.workflow.navigateToSurveyOverview(testContext.survey.id);

    // Create first tag
    const tagName = `UniqueTag_${Date.now()}`;
    testContext.createdTags.push(tagName);
    
    // Create tag using semantic locators
    const createTagButton = page.getByRole('button', { name: /create.*tag|new.*tag/i });
    await expect(createTagButton).toBeVisible({ timeout: 5000 });
    await createTagButton.click();
    
    const tagNameInput = page.getByLabel(/tag.*name|name/i).or(page.getByTestId('tag-name-input'));
    await tagNameInput.fill(tagName);
    
    const saveButton = page.getByRole('button', { name: /save|create/i });
    await saveButton.click();
    
    // Verify first tag created
    await expect(page.getByText(/tag.*created|success/i)).toBeVisible({ timeout: 5000 });

    // Try to create another tag with same name
    await createTagButton.click();
    await tagNameInput.fill(tagName); // Same name
    await saveButton.click();
    
    // Should show error using web-first assertion
    const errorMessage = page.getByText(/already exists|duplicate|name.*taken/i)
      .or(page.getByRole('alert'))
      .or(page.locator('.error, .alert-error'));
    
    await expect(errorMessage).toBeVisible({ timeout: 5000 });
  });

  test('should validate tag name requirements', async ({ page }) => {
    await tagsWorkflow.navigateToSurveyOverview(testSurvey.id);

    // Try to create tag with empty name
    await tagsWorkflow.attemptToCreateEmptyTag();
    
    // Should show validation error
    await expect(page.locator('text=/required|name/i')).toBeVisible();

    // Try to create tag with only spaces
    await tagsWorkflow.attemptToCreateTagWithSpaces();
    
    // Should show validation error or trim spaces
    await expect(page.locator('text=/required|invalid/i')).toBeVisible();
  });

  test('should persist tags across page navigation', async ({ page }) => {
    await tagsWorkflow.navigateToSurveyOverview(testSurvey.id);

    // Create and add tag to question
    const tagName = await tagsWorkflow.createNewTag();
    await tagsWorkflow.addTagToQuestion(tagName);

    // Navigate away and back
    await page.goto('/marketplace');
    await tagsWorkflow.navigateToSurveyOverview(testSurvey.id);

    // Verify tag is still on the question
    await tagsWorkflow.verifyTagAddedToQuestion(tagName);

    // Clean up
    await tagsWorkflow.deleteTagFromList(tagName);
  });

  test('should filter questions by tags', async ({ page }) => {
    await tagsWorkflow.navigateToSurveyOverview(testSurvey.id);

    // Create tags and apply to different questions
    const tag1 = await tagsWorkflow.createNewTag('Environment');
    const tag2 = await tagsWorkflow.createNewTag('Social');

    // Add tags to different questions
    await tagsWorkflow.addTagToSpecificQuestion(tag1, 0); // First question
    await tagsWorkflow.addTagToSpecificQuestion(tag2, 1); // Second question

    // Filter by first tag
    await tagsWorkflow.filterQuestionsByTag(tag1);
    
    // Verify only questions with tag1 are visible
    await tagsWorkflow.verifyFilteredQuestions(tag1);

    // Clear filter and verify all questions are visible again
    await tagsWorkflow.clearTagFilter();
    await tagsWorkflow.verifyAllQuestionsVisible();

    // Clean up tags
    await tagsWorkflow.deleteTagFromList(tag1);
    await tagsWorkflow.deleteTagFromList(tag2);
  });

  test('should display tag list correctly', async ({ page }) => {
    await tagsWorkflow.navigateToSurveyOverview(testSurvey.id);

    const tagNames = ['Environment', 'Social', 'Governance', 'Economic'];
    
    // Create multiple tags
    for (const tagName of tagNames) {
      await tagsWorkflow.createNewTag(tagName);
    }

    // Navigate to tags list/management page
    await tagsWorkflow.navigateToTagsList();

    // Verify all tags are displayed using semantic approach
    for (const tagName of tagNames) {
      await expect(page.getByText(tagName)).toBeVisible({ timeout: 5000 });
    }

    // Clean up all tags
    for (const tagName of tagNames) {
      await tagsWorkflow.deleteTagFromList(tagName);
    }
  });

  test('should handle tag operations with API validation', async ({ page }) => {
    await tagsWorkflow.navigateToSurveyOverview(testSurvey.id);

    // Create tag and verify API response
    const responsePromise = page.waitForResponse(response => 
      response.url().includes('/api/tags') && 
      response.request().method() === 'POST' &&
      response.status() === 200
    );

    const tagName = await tagsWorkflow.createNewTag();
    await responsePromise; // Wait for successful API response

    // Add tag to question and verify API response
    const addResponsePromise = page.waitForResponse(response => 
      response.url().includes('/api/') && 
      response.request().method() === 'PATCH' &&
      response.status() === 200
    );

    await tagsWorkflow.addTagToQuestion(tagName);
    await addResponsePromise;

    // Delete tag and verify API response
    const deleteResponsePromise = page.waitForResponse(response => 
      response.url().includes('/api/tags') && 
      response.request().method() === 'DELETE' &&
      response.status() === 200
    );

    await tagsWorkflow.deleteTagFromList(tagName);
    await deleteResponsePromise;
  });
});