import { test, expect } from '../../fixtures/auth.fixture';
import { SurveyBookmarksPage } from '../../pages';
import { TESTID } from '../../utils/constants';
import { generateUrl } from '../../utils/path';

test.describe('Survey Bookmarks - Simplified', () => {
  test.use({ storageState: 'playwright/.auth/manager.json' });
  
  let SurveyBookmarks: SurveyBookmarksPage;
  
  // Mock survey data
  const mockSurveyId = 'existing-survey-id';
  const testQuestions = [
    'What are your sustainability goals?',
    'How do you measure carbon emissions?',
    'What is your waste reduction strategy?'
  ];

  test.beforeEach(async ({ page }) => {
    SurveyBookmarks = new SurveyBookmarksPage(page);
    
    // Navigate directly to an existing survey
    // In real tests, you would create a survey first or use a known test survey
    await page.goto(generateUrl({ surveyId: mockSurveyId }).survey.overview);
    
    // Wait for survey to load
    await page.waitForSelector(`[data-testid="${TESTID.SURVEY.SURVEY_QUESTION_LIST}"]`, { timeout: 5000 });
  });

  test('should bookmark and unbookmark individual questions', async ({ page }) => {
    // Expand all questions
    await SurveyBookmarks.expandAllQuestions();
    
    // Bookmark first question
    await SurveyBookmarks.bookmarkQuestion(testQuestions[0]);
    
    // Verify it's bookmarked
    await SurveyBookmarks.verifyQuestionBookmarked(testQuestions[0]);
    
    // Verify bookmark count
    const count = await SurveyBookmarks.getBookmarkCount();
    expect(count).toBe(1);
    
    // Remove bookmark
    await SurveyBookmarks.removeBookmark(testQuestions[0]);
    
    // Verify it's not bookmarked
    await SurveyBookmarks.verifyQuestionNotBookmarked(testQuestions[0]);
  });

  test('should bookmark multiple questions', async ({ page }) => {
    // Bookmark multiple questions
    await SurveyBookmarks.bookmarkQuestions([testQuestions[0], testQuestions[1]]);
    
    // Verify both are bookmarked
    await SurveyBookmarks.verifyQuestionBookmarked(testQuestions[0]);
    await SurveyBookmarks.verifyQuestionBookmarked(testQuestions[1]);
    
    // Verify count
    const count = await SurveyBookmarks.getBookmarkCount();
    expect(count).toBe(2);
  });

  test('should filter bookmarked questions', async ({ page }) => {
    // Bookmark some questions
    await SurveyBookmarks.bookmarkQuestions([testQuestions[0], testQuestions[2]]);
    
    // Filter to show only bookmarked
    await SurveyBookmarks.filterBookmarkedQuestions();
    
    // Verify only bookmarked questions are visible
    const visibleQuestions = await page.locator('.trackingListRow').all();
    expect(visibleQuestions.length).toBe(2);
    
    // Clear filter
    await SurveyBookmarks.clearBookmarkFilter();
    
    // Verify all questions are visible again
    const allQuestions = await page.locator('.trackingListRow').all();
    expect(allQuestions.length).toBeGreaterThan(2);
  });

  test('should bulk bookmark questions', async ({ page }) => {
    // Expand all questions
    await SurveyBookmarks.expandAllQuestions();
    
    // Bulk bookmark
    await SurveyBookmarks.bulkBookmarkQuestions([testQuestions[0], testQuestions[1], testQuestions[2]]);
    
    // Verify all are bookmarked
    for (const question of testQuestions) {
      await SurveyBookmarks.verifyQuestionBookmarked(question);
    }
    
    const count = await SurveyBookmarks.getBookmarkCount();
    expect(count).toBe(3);
  });

  test('should export bookmarked questions', async ({ page }) => {
    // Bookmark questions
    await SurveyBookmarks.bookmarkQuestions(testQuestions);
    
    // Export bookmarks
    const download = await SurveyBookmarks.exportBookmarkedQuestions();
    
    // Verify download occurred
    expect(download).toBeTruthy();
    expect(download.suggestedFilename()).toContain('bookmark');
  });
});