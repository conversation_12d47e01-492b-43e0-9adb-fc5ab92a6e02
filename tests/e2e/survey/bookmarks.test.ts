import { test, expect } from '../../fixtures/auth.fixture';
import { SurveyBookmarksWorkflow } from '../../workflows/survey-bookmarks.workflow';
import { NetworkInterceptor } from '../../utils/network-interceptor';
import { GenerateData } from '../../utils/generate-data';
import { DEFAULT_DATA } from '../../setup/default-data';

// Enhanced test interface for better type safety
interface BookmarkTestContext {
  workflow: SurveyBookmarksWorkflow;
  survey: { id: string; name: string };
  networkInterceptor: NetworkInterceptor;
  questionsToBookmark: string[];
}

// These tests require manager role - use pre-authenticated state
test.describe('Survey Bookmarks Functionality', () => {
  // Specify that these tests should run with manager auth
  test.use({ storageState: 'playwright/.auth/manager.json' });
  
  let testContext: BookmarkTestContext;

  test.beforeEach(async ({ page, isolatedTest }) => {
    // Initialize enhanced test context
    const workflow = new SurveyBookmarksWorkflow(page);
    const networkInterceptor = new NetworkInterceptor(page);
    
    // Enable API logging for debugging
    await networkInterceptor.enableApiLogging();
    
    // Use existing survey from environment configuration
    const survey = {
      id: DEFAULT_DATA.survey.id,
      name: DEFAULT_DATA.survey.name
    };
    
    // Standard bookmark test questions - these will be found dynamically
    const questionsToBookmark: string[] = [];
    
    testContext = {
      workflow,
      survey,
      networkInterceptor,
      questionsToBookmark
    };
  });

  test.afterEach(async ({ page }) => {
    try {
      // Clear network interceptors if available
      if (testContext.networkInterceptor) {
        await testContext.networkInterceptor.clearAllInterceptors();
      }
      
      // Note: We're using an existing survey from env config, so no deletion needed
      // Just clear any bookmarks that were created during the test
      // This would ideally be done via API, but for now we rely on test isolation
    } catch (error) {
      console.warn('Cleanup failed:', error.message);
      // Don't fail the test due to cleanup issues
    }
  });

  test('should bookmark multiple questions successfully', async ({ page }) => {
    try {
      // Navigate directly to survey overview using URL
      await testContext.workflow.goToSurveyDirectly(
        DEFAULT_DATA.company.id,
        testContext.survey.id,
        'view'
      );
      
      // Wait for survey to load with web-first assertion
      await expect(page.getByText('October 2024 Annual').or(page.getByText(testContext.survey.name))).toBeVisible({ timeout: 2000 });
      await expect(page).toHaveURL(new RegExp(testContext.survey.id));
      
      // First, we need to click on a module to see questions
      // Click on "Assurance Metrics" module button to see questions
      const assuranceModule = page.getByRole('button', { name: 'Assurance Metrics' });
      if (await assuranceModule.isVisible({ timeout: 500 })) {
        await assuranceModule.click();
        await page.waitForTimeout(50); // Allow navigation to complete
        
        // Wait for a question to be visible
        await expect(page.getByText('Total weight of plastic packaging')).toBeVisible({ timeout: 2000 });
      }
      
      // First, check if there are already bookmarked questions and clear them
      const filledBookmarksInitial = page.locator('i.fa-solid.fa-bookmark');
      const filledCount = await filledBookmarksInitial.count();
      
      if (filledCount > 0) {
        // Clear existing bookmarks first
        for (let i = 0; i < filledCount; i++) {
          await filledBookmarksInitial.first().click();
          await page.waitForTimeout(50);
        }
      }
      
      // Now find all bookmark icons (empty bookmarks)
      const emptyBookmarks = page.locator('i.fa-light.fa-bookmark');
      const bookmarkCount = await emptyBookmarks.count();
      
      if (bookmarkCount === 0) {
        // Look for bookmark icons in general
        const anyBookmarkIcon = page.locator('i[class*="fa-bookmark"]');
        const anyCount = await anyBookmarkIcon.count();
        throw new Error(`No bookmarkable questions found on the page. Found ${anyCount} bookmark icons total.`);
      }
      
      // Bookmark the first 2 questions
      const questionsToBookmark = Math.min(2, bookmarkCount);
      
      for (let i = 0; i < questionsToBookmark; i++) {
        const bookmarkIcon = emptyBookmarks.nth(i);
        
        // Click the bookmark
        await bookmarkIcon.click();
        
        // Wait a bit for the bookmark to be saved
        await page.waitForTimeout(50);
      }
      
      // Verify bookmarks were applied
      const filledBookmarks = page.locator('i.fa-solid.fa-bookmark');
      await expect(filledBookmarks).toHaveCount(questionsToBookmark, { timeout: 2000 });
      
    } catch (error) {
      // Enhanced error handling with screenshot
      await page.screenshot({ 
        path: `test-results/bookmark-test-failed-${Date.now()}.png`,
        fullPage: true 
      });
      throw new Error(`Bookmark test failed: ${error.message}`);
    }
  });

  test('should toggle bookmark state correctly', async ({ page }) => {
    try {
      // Navigate directly to survey overview using URL
      await testContext.workflow.goToSurveyDirectly(
        DEFAULT_DATA.company.id,
        testContext.survey.id,
        'view'
      );
      
      // Wait for survey to load
      await expect(page.getByText('October 2024 Annual').or(page.getByText(testContext.survey.name))).toBeVisible({ timeout: 2000 });
      
      // Click on "Assurance Metrics" module to see questions
      const assuranceModule = page.getByRole('button', { name: 'Assurance Metrics' });
      if (await assuranceModule.isVisible({ timeout: 1500 })) {
        await assuranceModule.click();
        await page.waitForTimeout(3000);
        await expect(page.getByText('Total weight of plastic packaging')).toBeVisible({ timeout: 2000 });
      }
      
      // Find the first bookmark icon
      const emptyBookmarks = page.locator('i.fa-light.fa-bookmark');
      const firstEmptyBookmark = emptyBookmarks.first();
      
      // Click to bookmark
      await firstEmptyBookmark.click();
      await page.waitForTimeout(50);
      
      // Verify bookmark was applied
      const filledBookmarks = page.locator('i.fa-solid.fa-bookmark');
      await expect(filledBookmarks.first()).toBeVisible({ timeout: 1500 });

      // Remove bookmark by clicking filled icon
      await filledBookmarks.first().click();
      await page.waitForTimeout(50);
      
      // Verify bookmark was removed
      await expect(emptyBookmarks.first()).toBeVisible({ timeout: 1500 });
      await expect(filledBookmarks).toHaveCount(0);

      // Bookmark again to verify toggle works both ways
      await emptyBookmarks.first().click();
      await page.waitForTimeout(50);
      await expect(filledBookmarks.first()).toBeVisible({ timeout: 1500 });
      
    } catch (error) {
      await page.screenshot({ 
        path: `test-results/bookmark-toggle-failed-${Date.now()}.png`,
        fullPage: true 
      });
      throw new Error(`Toggle bookmark test failed: ${error.message}`);
    }
  });

  test('should handle bookmarking questions after expand all', async ({ page }) => {
    try {
      // Navigate directly to survey overview using URL
      await testContext.workflow.goToSurveyDirectly(
        DEFAULT_DATA.company.id,
        testContext.survey.id,
        'view'
      );

      // Wait for survey to load
      await expect(page.getByText('October 2024 Annual').or(page.getByText(testContext.survey.name))).toBeVisible({ timeout: 2000 });
      
      // Click on "Assurance Metrics" module to see questions
      const assuranceModule = page.getByRole('button', { name: 'Assurance Metrics' });
      if (await assuranceModule.isVisible({ timeout: 1500 })) {
        await assuranceModule.click();
        await page.waitForTimeout(3000);
        await expect(page.getByText('Total weight of plastic packaging')).toBeVisible({ timeout: 2000 });
      }

      // Look for expand all button (if available)
      const expandButton = page.getByRole('button', { name: /expand.*all|show.*all|collapse.*all/i })
        .or(page.getByTestId('expand-all-questions-btn'));
      
      if (await expandButton.isVisible({ timeout: 1500 })) {
        await expandButton.click();
        await page.waitForTimeout(300);
      }

      // Find all available bookmark icons after expansion
      const emptyBookmarks = page.locator('i.fa-light.fa-bookmark');
      const bookmarkCount = await emptyBookmarks.count();
      
      if (bookmarkCount === 0) {
        throw new Error('No bookmarkable questions found after expand all');
      }

      // Bookmark at least 2 questions (or all if less than 2)
      const questionsToBookmark = Math.min(2, bookmarkCount);
      
      for (let i = 0; i < questionsToBookmark; i++) {
        await emptyBookmarks.nth(i).click();
        await page.waitForTimeout(50);
      }
      
      // Verify bookmarks were applied
      const filledBookmarks = page.locator('i.fa-solid.fa-bookmark');
      await expect(filledBookmarks).toHaveCount(questionsToBookmark, { timeout: 2000 });
      
    } catch (error) {
      await page.screenshot({ 
        path: `test-results/bookmark-expand-all-failed-${Date.now()}.png`,
        fullPage: true 
      });
      throw new Error(`Expand all bookmark test failed: ${error.message}`);
    }
  });

  test('should persist bookmarks across page navigation', async ({ page }) => {
    try {
      // Navigate directly to survey overview using URL
      await testContext.workflow.goToSurveyDirectly(
        DEFAULT_DATA.company.id,
        testContext.survey.id,
        'view'
      );

      // Wait for survey to load
      await expect(page.getByText('October 2024 Annual').or(page.getByText(testContext.survey.name))).toBeVisible({ timeout: 2000 });
      
      // Click on "Assurance Metrics" module to see questions
      const assuranceModule = page.getByRole('button', { name: 'Assurance Metrics' });
      if (await assuranceModule.isVisible({ timeout: 1500 })) {
        await assuranceModule.click();
        await page.waitForTimeout(3000);
        await expect(page.getByText('Total weight of plastic packaging')).toBeVisible({ timeout: 2000 });
      }
      
      // Find and bookmark the first question
      const emptyBookmark = page.locator('i.fa-light.fa-bookmark').first();
      await emptyBookmark.click();
      await page.waitForTimeout(50);
      
      // Verify bookmark was applied
      const filledBookmark = page.locator('i.fa-solid.fa-bookmark').first();
      await expect(filledBookmark).toBeVisible({ timeout: 1500 });

      // Navigate away to marketplace
      await page.goto('/marketplace');
      await expect(page).toHaveURL(/\/marketplace/);
      await page.waitForTimeout(200);
      
      // Navigate back to the survey directly
      await testContext.workflow.goToSurveyDirectly(
        DEFAULT_DATA.company.id,
        testContext.survey.id,
        'view'
      );
      
      // Wait for survey to load again
      await expect(page.getByText('October 2024 Annual').or(page.getByText(testContext.survey.name))).toBeVisible({ timeout: 2000 });
      
      // Click on "Assurance Metrics" module again
      if (await assuranceModule.isVisible({ timeout: 1500 })) {
        await assuranceModule.click();
        await page.waitForTimeout(3000);
      }

      // Verify bookmark persistence
      const persistedBookmark = page.locator('i.fa-solid.fa-bookmark').first();
      await expect(persistedBookmark).toBeVisible({ timeout: 2000 });
      
    } catch (error) {
      await page.screenshot({ 
        path: `test-results/bookmark-persist-failed-${Date.now()}.png`,
        fullPage: true 
      });
      throw new Error(`Persist bookmark test failed: ${error.message}`);
    }
  });

  test('should display bookmark count correctly', async ({ page }) => {
    await testContext.workflow.navigateToSurveyOverview(testContext.survey.id);

    const questionsToTest = testContext.questionsToBookmark;

    // Bookmark multiple questions using modern approach
    for (const questionText of questionsToTest) {
      const questionRow = page.getByRole('listitem').filter({ hasText: questionText })
        .or(page.locator('.trackingListRow').filter({ hasText: questionText }));
      
      if (await questionRow.isVisible({ timeout: 1500 })) {
        const bookmarkButton = questionRow.locator('i.fa-light.fa-bookmark');
        if (await bookmarkButton.isVisible({ timeout: 500 })) {
          await bookmarkButton.click();
          // Wait for bookmark to be applied
          await expect(questionRow.locator('i.fa-solid.fa-bookmark')).toBeVisible({ timeout: 1500 });
        }
      }
    }

    // Verify the correct number of bookmarks using web-first assertion
    const allBookmarks = page.locator('i.fa-solid.fa-bookmark');
    await expect(allBookmarks).toHaveCount(questionsToTest.length, { timeout: 2000 });
  });

  test('should filter bookmarked questions', async ({ page }) => {
    try {
      // Navigate directly to survey overview using URL
      await testContext.workflow.goToSurveyDirectly(
        DEFAULT_DATA.company.id,
        testContext.survey.id,
        'view'
      );

      // Wait for survey to load
      await expect(page.getByText('October 2024 Annual').or(page.getByText(testContext.survey.name))).toBeVisible({ timeout: 2000 });
      
      // Check if there's a bookmark filter toggle/checkbox
      const bookmarkToggle = page.getByRole('switch', { name: /bookmark/i })
        .or(page.getByRole('checkbox', { name: /bookmark/i }))
        .or(page.getByText('Bookmarks').locator('..'));
      
      if (await bookmarkToggle.isVisible({ timeout: 1500 })) {
        // First, let's create some bookmarks
        // Click on "Assurance Metrics" module to see questions
        const assuranceModule = page.getByRole('button', { name: 'Assurance Metrics' });
        if (await assuranceModule.isVisible({ timeout: 1500 })) {
          await assuranceModule.click();
          await page.waitForTimeout(3000);
          await expect(page.getByText('Total weight of plastic packaging')).toBeVisible({ timeout: 2000 });
        }

        // Bookmark 2 questions
        const emptyBookmarks = page.locator('i.fa-light.fa-bookmark');
        const bookmarkCount = await emptyBookmarks.count();
        
        if (bookmarkCount >= 2) {
          // Bookmark first 2 questions
          await emptyBookmarks.nth(0).click();
          await page.waitForTimeout(50);
          await emptyBookmarks.nth(1).click();
          await page.waitForTimeout(50);
          
          // Verify bookmarks were applied
          const filledBookmarks = page.locator('i.fa-solid.fa-bookmark');
          await expect(filledBookmarks).toHaveCount(2);
          
          // Count total questions before filtering
          const allQuestions = page.locator('.trackingListRow, [role="listitem"]').filter({ hasText: /plastic|weight|total/i });
          const totalQuestions = await allQuestions.count();
          
          // Enable bookmark filter
          await bookmarkToggle.click();
          await page.waitForTimeout(200);
          
          // Verify only bookmarked questions are visible
          const visibleBookmarks = page.locator('i.fa-solid.fa-bookmark');
          await expect(visibleBookmarks).toHaveCount(2);
          
          // Disable bookmark filter
          await bookmarkToggle.click();
          await page.waitForTimeout(200);
          
          // Verify all questions are visible again
          const allQuestionsAfter = page.locator('.trackingListRow, [role="listitem"]').filter({ hasText: /plastic|weight|total/i });
          await expect(allQuestionsAfter).toHaveCount(totalQuestions);
        }
      } else {
        // Skip test if bookmark filter is not available
        console.log('Bookmark filter toggle not found, skipping filter test');
      }
      
    } catch (error) {
      await page.screenshot({ 
        path: `test-results/bookmark-filter-failed-${Date.now()}.png`,
        fullPage: true 
      });
      throw new Error(`Filter bookmark test failed: ${error.message}`);
    }
  });

  test('should handle bulk bookmark operations', async ({ page }) => {
    try {
      // Navigate directly to survey overview using URL
      await testContext.workflow.goToSurveyDirectly(
        DEFAULT_DATA.company.id,
        testContext.survey.id,
        'view'
      );

      // Wait for survey to load
      await expect(page.getByText('October 2024 Annual').or(page.getByText(testContext.survey.name))).toBeVisible({ timeout: 2000 });
      
      // Click on "Assurance Metrics" module to see questions
      const assuranceModule = page.getByRole('button', { name: 'Assurance Metrics' });
      if (await assuranceModule.isVisible({ timeout: 1500 })) {
        await assuranceModule.click();
        await page.waitForTimeout(3000);
        await expect(page.getByText('Total weight of plastic packaging')).toBeVisible({ timeout: 2000 });
      }

      // For this test, we'll simulate bulk operations by bookmarking multiple questions quickly
      const emptyBookmarks = page.locator('i.fa-light.fa-bookmark');
      const bookmarkCount = await emptyBookmarks.count();
      
      if (bookmarkCount >= 3) {
        // Bookmark at least 3 questions rapidly (simulating bulk operation)
        const questionsToBookmark = Math.min(3, bookmarkCount);
        
        // Click bookmarks quickly without waiting
        for (let i = 0; i < questionsToBookmark; i++) {
          await emptyBookmarks.nth(i).click();
          // Minimal wait between clicks
          await page.waitForTimeout(50);
        }
        
        // Wait a bit for all bookmarks to be saved
        await page.waitForTimeout(200);
        
        // Verify all bookmarks were applied
        const filledBookmarks = page.locator('i.fa-solid.fa-bookmark');
        await expect(filledBookmarks).toHaveCount(questionsToBookmark, { timeout: 2000 });
        
        // Now remove all bookmarks (bulk remove)
        for (let i = 0; i < questionsToBookmark; i++) {
          await filledBookmarks.first().click();
          await page.waitForTimeout(50);
        }
        
        // Wait for removals to complete
        await page.waitForTimeout(200);
        
        // Verify all bookmarks were removed
        await expect(filledBookmarks).toHaveCount(0, { timeout: 2000 });
      } else {
        throw new Error('Not enough questions available for bulk bookmark test');
      }
      
    } catch (error) {
      await page.screenshot({ 
        path: `test-results/bookmark-bulk-failed-${Date.now()}.png`,
        fullPage: true 
      });
      throw new Error(`Bulk bookmark test failed: ${error.message}`);
    }
  });

  test('should show bookmark icon states correctly', async ({ page }) => {
    // Navigate directly to survey overview using URL
    await testContext.workflow.goToSurveyDirectly(
      DEFAULT_DATA.company.id,
      testContext.survey.id,
      'view'
    );

    const questionText = 'Percentage of Suppliers Who Report on Sustainability';
    const questionRow = page.getByRole('listitem').filter({ hasText: questionText })
      .or(page.locator('.trackingListRow').filter({ hasText: questionText }));
    
    await expect(questionRow).toBeVisible({ timeout: 3000 });

    // Initially should show empty bookmark icon
    const emptyBookmark = questionRow.locator('i.fa-light.fa-bookmark');
    await expect(emptyBookmark).toBeVisible();

    // After bookmarking should show filled bookmark icon
    await emptyBookmark.click();
    const filledBookmark = questionRow.locator('i.fa-solid.fa-bookmark');
    await expect(filledBookmark).toBeVisible({ timeout: 1500 });

    // After removing bookmark should show empty icon again
    await filledBookmark.click();
    await expect(emptyBookmark).toBeVisible({ timeout: 1500 });
  });

  test('should handle bookmarks in different survey sections', async ({ page }) => {
    // Navigate directly to survey overview using URL
    await testContext.workflow.goToSurveyDirectly(
      DEFAULT_DATA.company.id,
      testContext.survey.id,
      'view'
    );

    // Expand all questions to see all sections
    const expandButton = page.getByRole('button', { name: /expand.*all|show.*all/i })
      .or(page.getByTestId('expand-all-questions-btn'));
    
    if (await expandButton.isVisible({ timeout: 1500 })) {
      await expandButton.click();
      await page.waitForTimeout(200);
    }

    // Bookmark questions from different sections
    const questionsFromDifferentSections = [
      'Percentage of Suppliers Who Report on Sustainability', // Section 1
      'Revenue - Small Scale Producers' // Section 2
    ];

    // Bookmark questions
    for (const questionText of questionsFromDifferentSections) {
      const questionRow = page.getByRole('listitem').filter({ hasText: questionText })
        .or(page.locator('.trackingListRow').filter({ hasText: questionText }));
      
      if (await questionRow.isVisible({ timeout: 1500 })) {
        const bookmarkButton = questionRow.locator('i.fa-light.fa-bookmark');
        await bookmarkButton.click();
        await expect(questionRow.locator('i.fa-solid.fa-bookmark')).toBeVisible({ timeout: 1500 });
      }
    }

    // Verify bookmarks persist across sections
    const sections = page.getByRole('button', { name: /section/i });
    const sectionCount = await sections.count();
    
    if (sectionCount > 0) {
      // Navigate through sections and verify bookmarks
      for (let i = 0; i < Math.min(sectionCount, 2); i++) {
        await sections.nth(i).click();
        await page.waitForTimeout(50);
        
        // Check if any bookmarked questions are visible in this section
        const bookmarkedItems = page.locator('i.fa-solid.fa-bookmark');
        if (await bookmarkedItems.count() > 0) {
          await expect(bookmarkedItems.first()).toBeVisible();
        }
      }
    }
  });

  test('should export bookmarked questions', async ({ page }) => {
    // Navigate directly to survey overview using URL
    await testContext.workflow.goToSurveyDirectly(
      DEFAULT_DATA.company.id,
      testContext.survey.id,
      'view'
    );

    const questionsToBookmark = [
      'Percentage of Suppliers Who Report on Sustainability',
      'Revenue - Small Scale Producers'
    ];

    // Bookmark questions
    for (const questionText of questionsToBookmark) {
      const questionRow = page.getByRole('listitem').filter({ hasText: questionText })
        .or(page.locator('.trackingListRow').filter({ hasText: questionText }));
      
      if (await questionRow.isVisible({ timeout: 1500 })) {
        const bookmarkButton = questionRow.locator('i.fa-light.fa-bookmark');
        await bookmarkButton.click();
        await expect(questionRow.locator('i.fa-solid.fa-bookmark')).toBeVisible({ timeout: 1500 });
      }
    }

    // Look for export functionality
    const exportButton = page.getByRole('button', { name: /export.*bookmark/i })
      .or(page.getByTestId('export-bookmarks-btn'));
    
    if (await exportButton.isVisible({ timeout: 1500 })) {
      // Intercept the download
      const downloadPromise = page.waitForEvent('download');
      await exportButton.click();
      
      try {
        const download = await downloadPromise;
        const filename = download.suggestedFilename();
        
        // Verify the download has a reasonable filename
        expect(filename).toMatch(/bookmark|export|survey/i);
      } catch (error) {
        // Export might open in a new window or use a different mechanism
        console.log('Export test: Download event not captured, export might use different mechanism');
      }
    } else {
      console.log('Export functionality not available in current survey view');
    }
  });
});