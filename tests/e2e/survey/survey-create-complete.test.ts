import { test, expect } from '../../fixtures/auth.fixture';
import { CompanyPage, SurveyPage, PageUtils } from '../../pages';
import { DEFAULT_DATA } from '../../setup/default-data';
import { GenerateData } from '../../utils/generate-data';
import { MESSAGES, TESTID, HTTP } from '../../utils/constants';
import { getApi, transformResponse } from '../../utils/api';
import { generateUrl } from '../../utils/path';
import { <PERSON><PERSON>, COOKIE_KEYS } from '../../utils/cookie';

test.describe('Survey Create and Complete', () => {
  test.use({ storageState: 'playwright/.auth/manager.json' });
  
  let Company: CompanyPage;
  let Survey: SurveyPage;
  let Utils: PageUtils;

  test.beforeEach(async ({ page, context }) => {
    Company = new CompanyPage(page);
    Survey = new SurveyPage(page);
    Utils = new PageUtils(page);
    
    // Initialize cookie helper with context
    Cookie.init(context, page.context().browser()?.version() || 'http://localhost:6001');
  });

  test('should create a new survey successfully', async ({ page }) => {
    // Given I am logged in as an administrator user within a specific organization
    await Company.selectByName(DEFAULT_DATA.company.name);

    // When I create a new survey by inputting details into a form
    await page.goto(generateUrl().survey.create);
    await expect(page.getByTestId(TESTID.SURVEY.SURVEY_NAME_INPUT)).toBeVisible();

    let surveyData = GenerateData.surveyData();
    await Survey.add({ ...surveyData, placeholder: DEFAULT_DATA.company.name });

    if (await page.getByText(MESSAGES.INVALID_SURVEY_INFORMATION).isVisible()) {
      // update month year when current one already existed
      const updateSurvey = GenerateData.surveyData();
      surveyData = { ...surveyData, month: updateSurvey.month, year: updateSurvey.year };
      await Survey.fillMonthYear(updateSurvey);
    }
    
    const promise = page.waitForResponse(getApi().survey.list);
    await page.getByTestId(TESTID.SURVEY.SURVEY_CONFIGS_SUBMIT_BUTTON).click();

    const { data: surveys = [] } = await transformResponse(await promise);
    const createdSurvey = surveys.find((s: any) => s.name === surveyData.name);
    
    if (createdSurvey) {
      const { _id, name, period, initiativeId, effectiveDate, type } = createdSurvey;
      await Cookie.addCookies(COOKIE_KEYS.SURVEY, { _id, name, period, initiativeId, effectiveDate, type });
    }

    // Then the new survey should be created successfully
    const currentSurvey = await Survey.getCurrentSurvey();
    expect(currentSurvey._id).toBeDefined();
  });

  test('should mark survey as complete and uncomplete', async ({ page }) => {
    // Assuming a survey was created in previous test or exists
    const currentSurvey = await Survey.getCurrentSurvey();
    if (!currentSurvey._id) {
      test.skip();
    }

    // Navigate to the survey
    await Survey.navigateToSurvey(currentSurvey._id);

    // When I mark a survey as complete
    await Utils.selectAppNavItem('Reporting');
    const { status, method } = await Survey.completeSurvey();
    expect(status).toEqual(200);
    expect(method).toEqual(HTTP.METHOD.PATCH);

    // Then the survey is changed to completed status
    await expect(page.getByTestId(TESTID.SURVEY.SURVEY_COMPLETE_BUTTON)).toHaveText('Report completed');

    // When I mark a survey as uncomplete
    const promise = page.waitForResponse(getApi().survey.complete);
    await page.getByTestId(TESTID.SURVEY.SURVEY_COMPLETE_BUTTON).click();
    const response = await transformResponse(await promise);
    expect(response.status).toEqual(200);
    expect(response.method).toEqual(HTTP.METHOD.DELETE);

    // Then the survey status should change to not completed
    await expect(page.getByTestId(TESTID.SURVEY.SURVEY_COMPLETE_BUTTON)).toHaveText('Mark as complete');
  });

  test.afterEach(async ({ page }) => {
    // Clean up - delete the survey if it was created
    const currentSurvey = await Survey.getCurrentSurvey();
    if (currentSurvey._id) {
      await Utils.selectAppNavItem('All reports');
      await Survey.deleteCurrentSurvey();
    }
  });
});