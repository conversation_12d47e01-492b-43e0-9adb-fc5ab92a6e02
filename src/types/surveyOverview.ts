export enum ViewValues {
  SdgTarget = 'sdg-target',
  Sdg = 'sdg',
  Materiality = 'materiality',
  Standards = 'standards',
  StandardsAndFrameworks = 'standards-and-frameworks',
  Custom = 'custom',
  StandardsQuestionList = 'standardsQuestionList',
  Frameworks = 'frameworks',
  Regulatory = 'regulatory',
  Ratings = 'ratings',
  Roles = 'roles',
  Users = 'users',
  Workgroups = 'workgroups',
  QuestionList = 'questionList',
  Group = 'group',
  CustomGroup = 'custom-group',
  QuestionPacks = 'question-packs',
  AssignedMetrics = 'assigned',
  AllPacks = 'all-packs',
  Archived = 'archived',
}

export enum SurveyOverviewMode {
  Universal,
  Scope,
  ScopeGroups,
  SdgAndScope,
  QuestionList,
}
