/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

export enum ReportDocumentTemplate {
  /** Empty state **/
  Blank = 'blank',
  /** Allow AI to Generate the initial template state **/
  AiGenerated = 'ai_generated',
  /** Simple template with a few pre-defined sections */
  Simple = 'simple',
}

export interface ReportDocumentConfig {
  template: ReportDocumentTemplate;
}

export interface ReportDocument {
  _id: string;
  title: string;
  description?: string;

  type: string;

  initiativeId: string;
  createdBy: string

  /** ISO string */
  lastUpdated: string;

  /** ISO string */
  created: string;

  /** @todo: Currently optional for existing data, should do migration to remove optional */
  config?: ReportDocumentConfig;
}

export type CreateReportDocumentMin = Pick<ReportDocument,
  | 'type'
  | 'title'
  | 'description'
  | 'initiativeId'
  | 'config'
> & { _id?: string };
