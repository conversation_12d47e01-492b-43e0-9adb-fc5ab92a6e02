/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { DataShareMin } from './dataShare';
import { InitiativePlain } from './initiative';
import { AssessmentType, CustomMetricsOrder, SurveyListItem } from './survey';
import { UniversalTrackerPlain } from './universalTracker';
import { CombinedDataScopeAccess } from './surveyDataScope';

export enum AccessType {
  Custom = 'custom',
  Assigned = 'assigned',
  Inherited = 'inherited',
}

export interface MetricGroupShare {
  initiativeId: string;
  created: Date;
  acceptedDate?: Date;
  initiative?: InitiativePlain;
}

export enum MetricGroupSourceType {
  Survey = 'survey',
}

interface MetricGroupSurveySource {
  type: MetricGroupSourceType.Survey;
  surveyId: string;
  jobId: string;
  topTopicsCount: number;
  topicUtrs: { _id: string }[];
}

type MetricGroupSource = MetricGroupSurveySource;

export interface MetricGroup {
  _id: string;
  groupName: string;
  code?: string;
  description?: string;
  initiativeId: string;
  initiative?: Pick<InitiativePlain, '_id' | 'name'>;
  universalTracker?: UniversalTrackerPlain[];
  universalTrackers?: string[];
  groupData?: {
    colour?: string,
    link?: string,
    icon?: string
    preferredAltCodes?: string[];
  };
  survey?: {
    _id: string;
    assessmentType: AssessmentType;
    completedDate?: string;
    effectiveDate: string;
  };
  source?: MetricGroupSource;
  metricsOrder?: CustomMetricsOrder;
  share?: MetricGroupShare[];
  accessType?: AccessType;
  createdBy?: string;
  created?: string;
  updated: string;
  parentId?: string;
  subgroups?: MetricGroup[];
}

export interface ImportMetricGroupParams {
  initiativeId: string,
  groupId: string,
  replace?: boolean;
  regenerate?: boolean;
  data: { QuestionCode: string, name?: string }[],
  mapping?: Record<string, string>
}

export enum MetricGroupType {
  Custom = 'custom',
  Tag = 'tag',
}

export type Tag = Pick<MetricGroup, '_id' | 'initiativeId' | 'universalTrackers' | 'groupName'>;

export interface CustomMetricsUsage {
  organisationCurrentUsage: number;
  organisationLimit: number;
  subsidiaryCurrentUsage: number;
}

export interface DataShareLookup {
  initiative: Pick<InitiativePlain, 'name' | 'permissionGroup' | 'appConfigCode'>,
  list: SurveyListItem[],
  dataShares: DataShareMin[],
  combinedDataScopeAccess?: CombinedDataScopeAccess,
  metricGroups?: Pick<MetricGroup, '_id' | 'groupName' | 'universalTrackers'>[]
}
