import { CalculatorInfo } from '@g17eco/types/carbon-calculator';
import { CTAdminBreadcrumbs } from '@apps/company-tracker/components/breadcrumbs/CTAdminBreadcrumbs';
import { G17Lightbox } from '@g17eco/molecules/lightbox';
import RequestDemoModal from '@features/request-demo-modal';
import { ROUTES } from '@constants/routes';
import {
  EmissionsCalculatorList,
  EmissionsCalculatorSidebar,
  useEmissionsCalculator,
} from '@features/emissions-calculator';
import { IntegrationModal } from '@features/integrations/IntegrationModal';
import { useIntegrationModal } from '@features/integrations/hooks/useIntegrationModal';
import { Loader } from '@g17eco/atoms/loader';
import { BasicAlert } from '@g17eco/molecules/alert';
import Dashboard, { DashboardSectionTitle } from '@g17eco/molecules/dashboard';
import { NoData } from '@g17eco/molecules/no-data';
import { QueryWrapper } from '@g17eco/molecules/query/QueryWrapper';
import { ProviderIntegrationStatus } from '@g17eco/types/app-integration';
import { useAppSelector } from '@reducers/index';
import { ProductCardFooter, ProductCards } from '@routes/home/<USER>/partials/ProductCards';
import { generateUrl } from '@routes/util';
import { getRootConfig } from '@selectors/globalData';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { getCurrentYear } from '@utils/date';
import { useHistory, useParams } from 'react-router-dom';
import { Button } from 'reactstrap';
import '../../../../css/common/emissions-calculator.scss';
import config from '../../../../config';
import { useListInitiativeAppIntegrationsQuery } from '../../../../api/initiative-app-integrations';

const emissionTrackerLogo = `${config.media.cdnUrl}/carbon-calculators/Emissions_Tracker_logo.svg`;

export const EmissionsCalculatorIntegrationRoute = () => {
  const history = useHistory();
  const { initiativeId } = useParams<{ initiativeId: string }>();
  const rootConfig = useAppSelector(getRootConfig);
  const canAccessIntegrations = FeaturePermissions.canAccessAppIntegrations(rootConfig);
  const { data, isLoading, error } = useListInitiativeAppIntegrationsQuery(initiativeId);
  const {
    calculatorsList,
    lightbox,
    handleSetLightBox,
    handleResetLightbox,
    handleGoToLink,
    requestModalOpen,
    toggleRequestDemoModal,
  } = useEmissionsCalculator();
  const { code, setCode, onLinkClick } = useIntegrationModal(initiativeId);

  const { allProviders = [], usedProviders = [] } = data ?? {};

  const currentYear = getCurrentYear();

  const goToSetup = (providerCode: string) => {
    history.push(generateUrl(ROUTES.APP_INTEGRATIONS_VIEW, { initiativeId, code: providerCode }));
  };

  const getCardButtons = (calculator: CalculatorInfo) => {
    const usedProvider = usedProviders.find((p) => p.code === calculator.code);
    if (usedProvider) {
      const btn = usedProvider.status === ProviderIntegrationStatus.Active
        ? { text: 'Access', onClick: () => onLinkClick({ link: usedProvider.login?.url, code: usedProvider.code }) }
        : { text: 'Pending', onClick: () => {}, disabled: true, color: 'secondary', outline: true }

      return [{ text: 'Details', onClick: () => setCode(usedProvider.code) }, btn];
    }

    // If integration is available and user has permissions, show Set up
    if (allProviders.some((p) => p.code === calculator.code) && canAccessIntegrations) {
      return [{ text: 'Set up', onClick: () => goToSetup(calculator.code), color: 'primary', outline: false }];
    }

    return [{ text: 'Contact us', onClick: toggleRequestDemoModal, color: 'secondary', outline: false }];
  };

  const cards = calculatorsList.data?.map((calculator) => {
    const status = usedProviders.find((p) => p.code === calculator.code)?.status;
    return {
      logo: emissionTrackerLogo,
      status,
      buttons: getCardButtons(calculator),
      footer: <ProductCardFooter imageUrl={calculator.logoFooter} />,
    };
  });

  const getButtons = (calculator: CalculatorInfo) => {
    const isAvailableIntegration = allProviders.some((p) => p.code === calculator.code);
    if (canAccessIntegrations && isAvailableIntegration && !usedProviders.some((p) => p.code === calculator.code)) {
      return [
        <Button key={calculator.code} color='primary' className='ml-2' onClick={() => goToSetup(calculator.code)}>
          Set up
        </Button>,
      ];
    }

    return [
      <Button key={calculator.code} color='secondary' className='ml-2' onClick={toggleRequestDemoModal}>
        Contact us
      </Button>,
    ];
  };

  return (
    <div className='d-flex flex-column'>
      <Dashboard>
        {isLoading ? <Loader /> : null}
        {error ? <BasicAlert type='danger'>{error.message}</BasicAlert> : null}
        <CTAdminBreadcrumbs
          initiativeId={initiativeId}
          breadcrumbs={[
            { label: 'Connections', url: generateUrl(ROUTES.APP_INTEGRATIONS, { initiativeId }) },
            { label: 'Emissions Calculator' },
          ]}
        />
        <DashboardSectionTitle
          title={`Best Emissions Calculators ${currentYear}: the successor to Carbon Calculators`}
          subtitle='Discover the best carbon emissions calculator for your needs'
          className='mt-3'
        />
        <div className='px-3'>
          <ProductCards cards={cards || []} />
          <div className='border-top border-ThemeBorderDefault mt-5 mx-auto' style={{ width: '100px' }} />
        </div>
      </Dashboard>
      <Dashboard className='calculator-comparison mt-0' hasSidebar={true} sidebarPosition='right'>
        <EmissionsCalculatorSidebar toggleRequestDemoModal={toggleRequestDemoModal} />
        <QueryWrapper
          query={calculatorsList}
          onError={() => <NoData text='There was an error fetching the calculators. Please try again.' />}
          onNoData={() => <NoData text='No calculators currently available. Please check back again later.' />}
          onSuccess={(calculators) => {
            return (
              <EmissionsCalculatorList
                calculators={calculators}
                handleGoToLink={handleGoToLink}
                handleSetLightBox={handleSetLightBox}
                getButtons={getButtons}
              />
            );
          }}
        />
        <G17Lightbox photoIndex={lightbox.index} slides={lightbox.slides} handleReset={handleResetLightbox} />
      </Dashboard>
      <RequestDemoModal isOpen={requestModalOpen} toggle={toggleRequestDemoModal} />
      <IntegrationModal
        code={code}
        initiativeId={initiativeId}
        closeModal={() => setCode(undefined)}
        handleLinkClick={onLinkClick}
      />
    </div>
  );
};
