/*
 * Copyright (c) 2024. Word Wide Generation Ltd
 */

import { AdminSettingSection, AdminSettings } from '@features/admin-settings/v2';
import { ROUTES } from '@constants/routes';
import { generateUrl } from '@routes/util';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { useAppSelector } from '@reducers/index';
import { isSingleOrg } from '@selectors/initiative';
import { isUserManagerByInitiativeId, isStaff } from '@selectors/user';
import NotAuthorised from '@routes/not-authorised';
import { ExtraFeature, FeatureStability } from '@g17eco/molecules/feature-stability';
import { getRootConfig } from '@selectors/globalData';
import { PACK, QUESTION, SURVEY } from '@constants/terminology';
import config from '../../../config';

export const AdminSettingsRoute = () => {
  const history = useHistory();
  const match = useRouteMatch<{ initiativeId?: string }>();
  const { initiativeId } = match.params;

  const isManager = useAppSelector((state) => isUserManagerByInitiativeId(state, initiativeId));
  const isUserStaff = useAppSelector(isStaff);
  const isK8s = !!config.isK8s;
  const rootConfig = useAppSelector(getRootConfig);
  const limitReportingLevels = FeaturePermissions.getLimitReportingLevels(rootConfig);
  const isSingleOrganization = useAppSelector(isSingleOrg);

  if (!initiativeId || !isManager) {
    return <NotAuthorised />;
  }

  const sections: AdminSettingSection[] = [
    {
      label: `Data & ${SURVEY.CAPITALIZED_ADJECTIVE} Configuration`,
      cards: [
        {
          icon: 'fal fa-ballot',
          text: `Manage custom ${PACK.PLURAL}`,
          clickHandler: () => history.push(`${generateUrl(ROUTES.CUSTOM_METRICS, { initiativeId })}/dashboard`),
          description: `Manage your custom ${PACK.PLURAL} effectively before including them in your ${SURVEY.SINGULAR} scope.`,
        },
        {
          icon: 'fal fa-file-circle-question',
          text: `Custom ${QUESTION.PLURAL}`,
          clickHandler: () => history.push(`${generateUrl(ROUTES.CUSTOM_METRICS, { initiativeId })}/manage`),
          description: `Easily create and oversee tailored ${QUESTION.PLURAL} for your organization's ${SURVEY.PLURAL}.`,
        },
        {
          icon: 'fal fa-list-check',
          text: `Manage ${QUESTION.PLURAL}`,
          clickHandler: () => history.push(`${generateUrl(ROUTES.ADMIN_DASHBOARD, { initiativeId })}/questions`),
          description: `Tailor your ${QUESTION.PLURAL} with overrides, custom dropdown options, and tagging.`,
        },
        {
          icon: 'fal fa-layer-group',
          text: `${SURVEY.CAPITALIZED_SINGULAR} templates`,
          clickHandler: () => history.push(`${generateUrl(ROUTES.SURVEY_TEMPLATES, { initiativeId })}`),
          description: `Mass generate ${SURVEY.PLURAL} across the organization from a single template.`,
        },
        {
          icon: 'fal fa-folder-gear',
          text: `Default ${SURVEY.SINGULAR} settings`,
          clickHandler: () => history.push(`${generateUrl(ROUTES.REPORT_SETTINGS, { initiativeId })}`),
          description: `Customize your ${SURVEY.SINGULAR} settings to streamline your workflow and save valuable time.`,
        },
      ],
    },
    {
      label: 'User & Platform Management',
      cards: [
        {
          icon: 'fal fa-users-gear',
          text: 'Manage users',
          clickHandler: () => history.push(generateUrl(ROUTES.MANAGE_USERS, { initiativeId })),
          description: 'Invite, manage, and remove users along with their access and delegation permissions.',
        },
        {
          icon: 'fal fa-sliders',
          text: 'Account settings',
          clickHandler: () => history.push(generateUrl(ROUTES.ACCOUNT_SETTINGS, { initiativeId })),
          description: 'Customize your companies information, adjust AI settings and manage payment details.',
        },
        {
          icon: 'fal fa-circle-nodes',
          text: (
            <>
              App Integrations
              <FeatureStability className={'ms-2'} feature={ExtraFeature.PartnerIntegration} />
            </>
          ),
          clickHandler: () => history.push(generateUrl(ROUTES.APP_INTEGRATIONS, { initiativeId })),
          description: 'Integrate with other Product offered by G17Eco Materiality Tracker/Emissions Tracker.',
        },
        {
          icon: 'fal fa-circle-nodes',
          text: (
            <>
              Data Integrations
              <FeatureStability className={'ms-2'} feature={ExtraFeature.DataIntegrations} />
            </>
          ),
          clickHandler: () => history.push(generateUrl(ROUTES.DATA_INTEGRATIONS, { initiativeId })),
          description: 'Integrate with other external data sources.',
          isHidden: !isUserStaff || !isK8s,
        },
      ],
    },
    {
      label: 'Data Management & Collaboration',
      cards: [
        {
          icon: 'fal fa-folders',
          text: 'Document library',
          clickHandler: () => history.push(generateUrl(ROUTES.DOCUMENT_LIBRARY, { initiativeId })),
          description: `Manage documents uploaded to your organization, including ${QUESTION.SINGULAR} evidence.`,
        },
        {
          icon: 'fal fa-file-import',
          text: 'Bulk importing',
          clickHandler: () => history.push(`${generateUrl(ROUTES.BULK_IMPORTING, { initiativeId })}`),
          description: `Import data in bulk to existing ${SURVEY.PLURAL} for all organization subsidiaries in a single location.`,
        },
        {
          icon: 'fal fa-share-from-square',
          text: 'Data sharing',
          clickHandler: () => history.push(generateUrl(ROUTES.DATA_SHARE_INITIATIVE, { initiativeId })),
          description: 'Control who can view and access your organization’s data.',
        },
      ],
    },
    {
      label: 'Monitoring & Insights',
      cards: [
        {
          icon: 'fal fa-chart-mixed',
          text: 'Analytics dashboard',
          clickHandler: () => history.push(generateUrl(ROUTES.ADMIN_DASHBOARD, { initiativeId })),
          description: 'Track user performance and progress while managing messages from a unified place.',
          disabledText:
            limitReportingLevels === 1 && isSingleOrganization
              ? 'Add additional subsidiaries to your organization map to enable this feature'
              : '',
        },
        {
          icon: 'fal fa-file-waveform',
          text: 'System logs',
          clickHandler: () => history.push(generateUrl(ROUTES.SYSTEM_LOG, { initiativeId })),
          description: 'Track platform events, helping troubleshoot issues and monitor performance effectively.',
        },
      ],
    },
  ];
  return <AdminSettings sections={sections} />;
};
