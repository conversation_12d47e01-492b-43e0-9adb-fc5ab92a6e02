/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import { useHistory, useParams } from 'react-router';
import { QueryWrapper } from '@g17eco/molecules/query/QueryWrapper';
import { useListReportDocumentsQuery } from '@api/initiative-report-documents';
import { ReportDocumentList } from '@apps/company-tracker/routes/report-editor/components/ReportDocumentList';
import Dashboard from '@g17eco/molecules/dashboard';
import { ReportDocument } from '@g17eco/types/reportDocument';
import { generateUrl } from '@routes/util';
import { ROUTES } from '@constants/routes';


export const ReportEditorListRoute = () => {

  const history = useHistory();
  const { initiativeId, type } = useParams<{ initiativeId: string, type?: string }>();
  const query = useListReportDocumentsQuery({ initiativeId })

  const onView = (report: ReportDocument) => {
    history.push(generateUrl(ROUTES.REPORT_EDITOR_VIEW, {
      type,
      initiativeId: report.initiativeId,
      reportId: report._id
    }));
  }

  const onEdit = (report: ReportDocument) => {
    history.push(generateUrl(ROUTES.REPORT_EDITOR_EDIT, {
      type,
      initiativeId: report.initiativeId,
      reportId: report._id
    }));
  }

  const viewCreate = () => {
    history.push(generateUrl(ROUTES.REPORT_EDITOR_CREATE, { initiativeId, type }));
  };

  const onRender = (reports: ReportDocument[] = []) => {
    return <ReportDocumentList viewCreate={viewCreate} onView={onView} onEdit={onEdit} type={type} list={reports} />;
  };

  return (
    <Dashboard>
      <QueryWrapper query={query} onSuccess={onRender} onNoData={onRender} />
    </Dashboard>
  );
}
