/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { ReportDocument } from '@g17eco/types/reportDocument';
import { DashboardSection } from '@g17eco/molecules/dashboard';
import { Button } from 'reactstrap';
import { ColumnDef, Table } from '@g17eco/molecules/table';
import IconButton from '@g17eco/molecules/button/IconButton';
import { formatDateUTC } from '@utils/date';

interface Props {
  list: ReportDocument[]
  type?: string;
  onView: (report: ReportDocument) => void;
  onEdit: (report: ReportDocument) => void;
  viewCreate: () => void;
  handleDelete?: (report: ReportDocument) => void;
}

export const ReportDocumentList = (props: Props) => {
  const { list, type, onView, onEdit, handleDelete, viewCreate } = props;

  const reports = type ? list.filter(report => report.type === type) : list;

  const buttons = [
    <Button color={'primary'} key={'create'} onClick={viewCreate}>Create</Button>
  ];

  const columns: ColumnDef<ReportDocument>[] = [
    {
      header: 'Report',
      accessorKey: 'title',
      meta: {
        cellProps: {
          style: {
            width: 350,
            maxWidth: 350,
          },
        },
      },
    },
    {
      accessorKey: 'description',
    },
    {
      accessorKey: 'type',
    },
    {
      id: 'created',
      accessorFn: (value) => formatDateUTC(value.created),
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        return (
          <div>
            {handleDelete ? (
              <IconButton
                outline={false}
                color='transparent'
                icon='fal fa-trash'
                onClick={() => handleDelete?.(row.original)}
              />
            ) : null}
            <IconButton
              outline={false}
              color='transparent'
              icon='fal fa-eye'
              onClick={() => onView(row.original)}
            />
            <IconButton
              outline={false}
              color='transparent'
              icon='fal fa-pencil'
              onClick={() => onEdit(row.original)}
            />
          </div>
        );
      },
    }
  ];

  return (
    <DashboardSection title={`Report Document List (${(type ?? 'all').toUpperCase()})`} buttons={buttons}>
      <Table columns={columns} data={reports} />
    </DashboardSection>
  );
}
