import { describe, it, expect, vi, beforeEach } from 'vitest';
import { setup, screen, waitFor } from '@fixtures/utils';
import { createTestSetup } from '@fixtures/test-setup';
import { http } from 'msw';
import { getUrl, ResponseSuccess, ResponseError } from '@fixtures/msw-fixtures';
import { CarbonCalculatorComparisonRoute } from './index';
import { CalculatorInfo } from '@g17eco/types/carbon-calculator';
import { reduxFixtureStore } from '@fixtures/redux-store';

// Create test setup with MSW
const testSetup = createTestSetup();

// Mock analytics to avoid side effects
vi.mock('@services/analytics/AnalyticsService', () => ({
  getAnalytics: () => ({
    track: vi.fn(),
  }),
}));

describe('CarbonCalculatorComparisonRoute', () => {
  // Test data fixtures
  const mockCalculators: CalculatorInfo[] = [
    {
      code: 'greenly',
      name: '<PERSON><PERSON>',
      logo: 'https://example.com/greenly-logo.jpeg',
      logoFooter: 'https://example.com/greenly-logo-footer.png',
      link: 'https://greenly.earth/',
      tags: ['Emissions report', 'Dashboards'],
      description: 'Greenly is a carbon accounting company',
      highlights: ['All industries', 'Scope 1,2,3'],
      restrictions: ['Some features on higher plans only'],
      // No integration - should always show Contact Us
    },
    {
      code: 'green-project-tech',
      name: 'Green Project Technologies',
      logo: 'https://example.com/gpt-logo.png',
      logoFooter: 'https://example.com/gpt-logo-footer.png',
      link: 'https://www.greenprojecttech.com/',
      tags: ['All industries', 'Dashboards'],
      description: 'Green Project Technologies platform',
      highlights: ['Carbon accounting', 'Dashboards'],
      restrictions: [],
      integration: {
        type: 'iframe' as const,
        iframe: {
          src: 'https://app.greenprojecttech.com/dashboard',
        },
      },
      // Has integration - should show Set Up for staff with query param
    },
  ];

  // Helper to render component with store and optional route
  const renderComponent = (props = {}, routeOptions?: { search?: string }) => {
    const store = reduxFixtureStore();
    const route = {
      initialEntries: [`/emissions-calculator${routeOptions?.search || ''}`],
      path: '/emissions-calculator'
    };
    return setup(<CarbonCalculatorComparisonRoute {...props} />, { store, route });
  };

  // Setup MSW lifecycle hooks
  testSetup.setupHooks();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('rendering', () => {
    it('should render calculator tiles with correct information', async () => {
      testSetup.server.use(
        http.get(getUrl('/o/carbon-calculators'), () => {
          return ResponseSuccess(mockCalculators);
        })
      );

      renderComponent();

      await waitFor(() => {
        expect(screen.getByText('Greenly')).toBeInTheDocument();
        expect(screen.getByText('Green Project Technologies')).toBeInTheDocument();
      });

      // Check descriptions are rendered
      expect(screen.getByText('Greenly is a carbon accounting company')).toBeInTheDocument();
      expect(screen.getByText('Green Project Technologies platform')).toBeInTheDocument();
    });

    it('should render the sidebar with FAQs', async () => {
      testSetup.server.use(
        http.get(getUrl('/o/carbon-calculators'), () => {
          return ResponseSuccess(mockCalculators);
        })
      );

      renderComponent();

      await waitFor(() => {
        expect(screen.getByText(/Best Emissions Calculators/)).toBeInTheDocument();
      });

      // Check sidebar content
      expect(screen.getByText('Still have questions?')).toBeInTheDocument();
    });
  });

  describe('user interactions', () => {
    describe('Contact Us button', () => {
      it('should show Contact Us button for calculators without integration', async () => {
        testSetup.server.use(
          http.get(getUrl('/o/carbon-calculators'), () => {
            return ResponseSuccess([mockCalculators[0]]); // Greenly without integration
          })
        );

        const { user } = renderComponent();

        await waitFor(() => {
          expect(screen.getByText('Greenly')).toBeInTheDocument();
        });

        const contactUsButtons = screen.getAllByRole('button', { name: /contact us/i });
        expect(contactUsButtons.length).toBeGreaterThan(0);
        expect(contactUsButtons[0]).toHaveTextContent('Contact Us');

        // Test clicking opens modal
        await user.click(contactUsButtons[0]);
        expect(contactUsButtons[0]).toBeInTheDocument();
      });

      it('should show Contact Us button when integration exists but no query param', async () => {
        testSetup.server.use(
          http.get(getUrl('/o/carbon-calculators'), () => {
            return ResponseSuccess([mockCalculators[1]]); // GPT with integration
          })
        );

        renderComponent();

        await waitFor(() => {
          expect(screen.getByText('Green Project Technologies')).toBeInTheDocument();
        });

        // Without query param, even integrated calculators show Contact Us
        const contactUsButtons = screen.getAllByRole('button', { name: /contact us/i });
        expect(contactUsButtons.length).toBeGreaterThan(0);
      });
    });

    describe('Set Up button', () => {
      it('should show Set Up button for integrated calculators with query param', async () => {
        testSetup.server.use(
          http.get(getUrl('/o/carbon-calculators'), () => {
            return ResponseSuccess([mockCalculators[1]]); // GPT with integration
          })
        );

        renderComponent({}, { search: '?integrations=true' });

        await waitFor(() => {
          expect(screen.getByText('Green Project Technologies')).toBeInTheDocument();
        });

        const setUpButton = screen.getByRole('button', { name: /set up/i });
        expect(setUpButton).toBeInTheDocument();
        expect(setUpButton).toHaveTextContent('Set Up');
      });

      it('should trigger navigation when Set Up is clicked', async () => {
        testSetup.server.use(
          http.get(getUrl('/o/carbon-calculators'), () => {
            return ResponseSuccess([mockCalculators[1]]);
          })
        );

        const { user } = renderComponent({}, { search: '?integrations=true' });

        await waitFor(() => {
          expect(screen.getByText('Green Project Technologies')).toBeInTheDocument();
        });

        const setUpButton = screen.getByRole('button', { name: /set up/i });
        
        // The button click will trigger navigation via history.push
        // We're not mocking it, so we just verify the button is clickable
        await user.click(setUpButton);
        
        // The button should still be there after click (navigation is handled by the router)
        expect(setUpButton).toBeInTheDocument();
      });
    });

    describe('mixed calculator types', () => {
      it('should show correct buttons without query param', async () => {
        testSetup.server.use(
          http.get(getUrl('/o/carbon-calculators'), () => {
            return ResponseSuccess(mockCalculators);
          })
        );

        renderComponent();

        await waitFor(() => {
          expect(screen.getByText('Greenly')).toBeInTheDocument();
          expect(screen.getByText('Green Project Technologies')).toBeInTheDocument();
        });

        // Both should show Contact Us without query param
        const contactUsButtons = screen.getAllByRole('button', { name: /contact us/i });
        expect(contactUsButtons).toHaveLength(2);
      });

      it('should show different buttons with integrations query param', async () => {
        testSetup.server.use(
          http.get(getUrl('/o/carbon-calculators'), () => {
            return ResponseSuccess(mockCalculators);
          })
        );

        renderComponent({}, { search: '?integrations=true' });

        await waitFor(() => {
          expect(screen.getByText('Greenly')).toBeInTheDocument();
          expect(screen.getByText('Green Project Technologies')).toBeInTheDocument();
        });

        // Greenly: Contact Us (no integration)
        // GPT: Set Up (has integration + query param)
        const contactUsButtons = screen.getAllByRole('button', { name: /contact us/i });
        const setUpButtons = screen.getAllByRole('button', { name: /set up/i });
        
        expect(contactUsButtons).toHaveLength(1);
        expect(setUpButtons).toHaveLength(1);
      });
    });
  });

  describe('edge cases', () => {
    it('should handle loading state', () => {
      // Set up a handler that never resolves to simulate loading
      testSetup.server.use(
        http.get(getUrl('/o/carbon-calculators'), async () => {
          await new Promise(() => {}); // Never resolves
        })
      );

      renderComponent();

      // Should show loading indicator
      expect(screen.getByTestId('loader')).toBeInTheDocument();
    });

    it('should handle error state', async () => {
      testSetup.server.use(
        http.get(getUrl('/o/carbon-calculators'), () => {
          return ResponseError({ 
            message: 'Server error',
            status: 500 
          });
        })
      );

      renderComponent();

      await waitFor(() => {
        expect(screen.getByText(/error fetching the calculators/i)).toBeInTheDocument();
      });
    });

    it('should handle empty data state', async () => {
      testSetup.server.use(
        http.get(getUrl('/o/carbon-calculators'), () => {
          return ResponseSuccess([]);
        })
      );

      renderComponent();

      await waitFor(() => {
        expect(screen.getByText(/no calculators currently available/i)).toBeInTheDocument();
      });
    });

    it('should handle calculators without optional fields', async () => {
      const minimalCalculator: CalculatorInfo = {
        code: 'minimal',
        name: 'Minimal Calculator',
        logo: 'https://example.com/logo.png',
        logoFooter: 'https://example.com/logo-footer.png',
        tags: [],
        description: 'Minimal description',
        highlights: [],
        restrictions: [],
        // No link, no integration
      };

      testSetup.server.use(
        http.get(getUrl('/o/carbon-calculators'), () => {
          return ResponseSuccess([minimalCalculator]);
        })
      );

      renderComponent();

      await waitFor(() => {
        expect(screen.getByText('Minimal Calculator')).toBeInTheDocument();
      });

      // Should still show Contact Us button
      const contactUsButton = screen.getByRole('button', { name: /contact us/i });
      expect(contactUsButton).toBeInTheDocument();
    });
  });

  describe('accessibility', () => {
    it('should have proper button labels and icons', async () => {
      testSetup.server.use(
        http.get(getUrl('/o/carbon-calculators'), () => {
          return ResponseSuccess(mockCalculators);
        })
      );

      renderComponent({}, { search: '?integrations=true' });

      await waitFor(() => {
        expect(screen.getByText('Greenly')).toBeInTheDocument();
      });

      // Check Contact Us button has proper icon
      const contactUsButton = screen.getAllByRole('button', { name: /contact us/i })[0];
      expect(contactUsButton.querySelector('.fal.fa-envelope')).toBeInTheDocument();

      // Check Set Up button has proper icon
      const setUpButton = screen.getByRole('button', { name: /set up/i });
      expect(setUpButton.querySelector('.fal.fa-eye')).toBeInTheDocument();
    });

    it('should support keyboard navigation', async () => {
      testSetup.server.use(
        http.get(getUrl('/o/carbon-calculators'), () => {
          return ResponseSuccess([mockCalculators[0]]);
        })
      );

      const { user } = renderComponent();

      await waitFor(() => {
        expect(screen.getByText('Greenly')).toBeInTheDocument();
      });

      // Tab to Contact Us button
      await user.tab();
      const contactUsButton = screen.getAllByRole('button', { name: /contact us/i })[0];
      
      // Button should be focusable
      expect(contactUsButton).toHaveProperty('type', 'button');
    });
  });

  describe('query parameter handling', () => {
    it('should parse integrations query param correctly', async () => {
      testSetup.server.use(
        http.get(getUrl('/o/carbon-calculators'), () => {
          return ResponseSuccess([mockCalculators[1]]);
        })
      );

      // Test with different query param formats
      renderComponent({}, { search: '?integrations=true&other=param' });

      await waitFor(() => {
        expect(screen.getByText('Green Project Technologies')).toBeInTheDocument();
      });

      // Should still show Set Up button with additional query params
      const setUpButton = screen.getByRole('button', { name: /set up/i });
      expect(setUpButton).toBeInTheDocument();
    });

    it('should handle missing integrations query param', async () => {
      testSetup.server.use(
        http.get(getUrl('/o/carbon-calculators'), () => {
          return ResponseSuccess([mockCalculators[1]]);
        })
      );

      // Test with other query params but not integrations
      renderComponent({}, { search: '?other=param' });

      await waitFor(() => {
        expect(screen.getByText('Green Project Technologies')).toBeInTheDocument();
      });

      // Should show Contact Us button without integrations param
      const contactUsButton = screen.getByRole('button', { name: /contact us/i });
      expect(contactUsButton).toBeInTheDocument();
    });
  });
});