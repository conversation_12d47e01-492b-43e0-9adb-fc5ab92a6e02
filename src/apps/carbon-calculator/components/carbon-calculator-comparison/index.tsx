/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import '../../../../css/common/emissions-calculator.scss';
import React from 'react';
import Dashboard, { DashboardSectionTitle } from '@g17eco/molecules/dashboard';
import { Button } from 'reactstrap';
import { NoData } from '@g17eco/molecules/no-data';
import { EmissionsCalculatorList, EmissionsCalculatorSidebar, useEmissionsCalculator } from '@features/emissions-calculator';
import { QueryWrapper } from '@g17eco/molecules/query/QueryWrapper';
import RequestDemoModal from '@features/request-demo-modal';
import { G17Lightbox } from '@g17eco/molecules/lightbox';
import { getAnalytics } from '@services/analytics/AnalyticsService';
import { AnalyticsEvents } from '@services/analytics/AnalyticsEvents';
import { useHistory } from 'react-router-dom';
import { CalculatorInfo } from '@g17eco/types/carbon-calculator';
import { generateUrl } from '@routes/util';
import { ROUTES } from '@constants/routes';
import queryString from 'query-string';
import { getCurrentYear } from '@utils/date';


export const CarbonCalculatorComparisonRoute = () => {
  const history = useHistory();
  const {
    calculatorsList,
    lightbox,
    handleSetLightBox,
    handleResetLightbox,
    requestModalOpen,
    toggleRequestDemoModal,
    handleGoToLink,
  } = useEmissionsCalculator();

  const query = queryString.parse(history.location.search);
  const currentYear = getCurrentYear();


  const handleView = (code: string) => {
    getAnalytics().track(AnalyticsEvents.EmissionCalculatorView, { code })
    history.push(generateUrl(ROUTES.EMISSIONS_PARTNER, { partnerCode: code }));
  }

  const getButtons = ({ code, integration }: CalculatorInfo): React.JSX.Element[] => {
    // If integration is enabled (and query param present for staff), show Set Up button
    if (integration && query.integrations) {
      return [
        <Button key={code} color='primary' className={'ml-2'} outline onClick={() => handleView(code)}>
          Set Up<i className='ms-2 fal fa-eye' />
        </Button>
      ]
    }
    // If no integration enabled, show Contact Us button
    return [
      <Button key={code} color='primary' className={'ml-2'} outline onClick={toggleRequestDemoModal}>
        Contact Us<i className='ms-2 fal fa-envelope' />
      </Button>
    ];
  }

  return (
    <div className='d-flex flex-column'>
      <Dashboard className='mb-0'>
        <DashboardSectionTitle
          title={`Best Emissions Calculators ${currentYear}: the successor to Carbon Calculators`}
          subtitle='Discover the best carbon emissions calculator for your needs'
        />
      </Dashboard>
      <Dashboard className='calculator-comparison mt-0' hasSidebar={true} sidebarPosition='right'>
        <EmissionsCalculatorSidebar toggleRequestDemoModal={toggleRequestDemoModal} />
        <QueryWrapper
          query={calculatorsList}
          onError={() => <NoData text='There was an error fetching the calculators. Please try again.' />}
          onNoData={() => <NoData text='No calculators currently available. Please check back again later.' />}
          onSuccess={(calculators) => (
            <EmissionsCalculatorList
              calculators={calculators}
              handleGoToLink={handleGoToLink}
              handleSetLightBox={handleSetLightBox}
              getButtons={getButtons}
            />
          )}
        />
        <G17Lightbox photoIndex={lightbox.index} slides={lightbox.slides} handleReset={handleResetLightbox} />
      </Dashboard>
      <RequestDemoModal isOpen={requestModalOpen} toggle={toggleRequestDemoModal} />
    </div>
  );
}

