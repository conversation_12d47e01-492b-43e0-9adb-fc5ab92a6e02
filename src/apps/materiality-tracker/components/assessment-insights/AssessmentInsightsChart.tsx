import { AssessmentData, ESGCategory, MaterialPillar } from '@apps/materiality-tracker/api/materiality-assessment';
import { Loader } from '@g17eco/atoms/loader';
import Chart from 'react-google-charts';
import './styles.scss';
import variables from '../../../../css/variables.module.scss';
import {
  DEFAULT_CHART_COLORS,
  LegendType,
  MATERIALITY_CHART_COLORS,
  chartConfigs,
  chartStyles,
  getChartEvents,
} from './chart-utils';
import { BasicAlert } from '@g17eco/molecules/alert';
import { isScoreInRange, NOT_FOUND_CATEGORY_MESSAGE } from './utils';

const WIDTH = '100%';
const HEIGHT = 320;
const COLUMN_WIDTH = '93%';

export const AssessmentInsightsChart = ({
  data,
  selectedTopicCode,
  columnClickHandler,
  category,
}: {
  data: AssessmentData[];
  selectedTopicCode: string;
  columnClickHandler: (index: number) => void;
  category: LegendType;
}) => {
  const selectedIndex = data.findIndex((topic) => topic.code === selectedTopicCode);

  const config = chartConfigs.find((config) => config.code === category);

  if (!config) {
    return <BasicAlert type='warning'>{NOT_FOUND_CATEGORY_MESSAGE}</BasicAlert>;
  }

  const chartData = [
    [
      'Topic',
      ...Object.values(config.categories)
        .map(({ label }) => [label, { role: 'style' }])
        .flat(),
    ],
    ...data.map((topic, index) => [
      topic.name,
      ...Object.keys(config.categories)
        .map((code) => {
          let score = NaN;
          switch (category) {
            case LegendType.ESG:
              score = topic.categories?.esg?.includes(code as ESGCategory) ? topic.score : NaN;
              break;
            case LegendType.MaterialPillar:
              score = topic.categories?.materialPillar?.includes(code as MaterialPillar) ? topic.score : NaN;
              break;
            case LegendType.Materiality: {
              const { min, max } = config.categories[code];
              score = isScoreInRange({ score: topic.relativeScore, min, max }) ? topic.score : NaN;
              break;
            }
          }
          const style = index === selectedIndex ? 'opacity: 1' : 'opacity: 0.25';
          return [score, style];
        })
        .flat(),
    ]),
  ];

  const chartEvents = getChartEvents(columnClickHandler);

  return (
    <div className='assessment__insights-chart'>
      <Chart
        chartType='ColumnChart'
        data={chartData}
        chartEvents={chartEvents}
        loader={<Loader />}
        options={{
          isStacked: true,
          bar: {
            groupWidth: COLUMN_WIDTH,
          },
          chartArea: chartStyles.chartArea,
          hAxis: {
            textPosition: 'none',
          },
          vAxis: {
            ...chartStyles.axis,
            title: 'TOPIC SCORE',
          },
          baselineColor: variables.ColourWhite,
          legend: chartStyles.legend,
          colors: category === LegendType.Materiality ? MATERIALITY_CHART_COLORS : DEFAULT_CHART_COLORS,
        }}
        width={WIDTH}
        height={HEIGHT}
      />
    </div>
  );
};
