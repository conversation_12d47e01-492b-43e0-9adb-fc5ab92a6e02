import {
  boundaryMap,
  esgMap,
  isDoubleMaterialityAssessmentData,
  materialPillarMap,
} from '@apps/materiality-tracker/utils';
import { GlossaryText } from '@g17eco/molecules/glossary-text/GlossaryText';
import { ExpandableParagraph } from '@g17eco/molecules/paragraph';
import { useAppSelector } from '@reducers/index';
import { Col, Row } from 'reactstrap';
import { PrioritizedAssessmentData } from './types';

const Detail = ({ label, icon, color }: { label: string; icon?: string; color?: string }) => {
  return (
    <div className='d-flex align-items-center' style={{ height: '28px' }}>
      {icon ? <i className={`fa-fw fal ${icon} ${color ?? 'text-ThemeIconDark'} mr-2 text-xl`} /> : null}
      {label}
    </div>
  );
};

export const MaterialTopicDetails = ({ data, isCompact }: { data: PrioritizedAssessmentData; isCompact?: boolean }) => {
  const { relativeScore, categories = {}, description, action } = data;
  const { esg = [], materialPillar = [], boundary = [] } = categories;

  const glossaryState = useAppSelector((state) => state.glossary.data);
  const isDoubleMateriality = isDoubleMaterialityAssessmentData(data);
  const col = isCompact ? 'col-6' : 'col-3';
  return (
    <div className={isCompact ? '' : 'background-ThemeBgMedium p-3 mt-3 rounded'}>
      {isCompact ? null : <h4 className='text-ThemeTextDark'>{data.name}</h4>}
      <Row className={isCompact ? '' : 'mt-3'}>
        <Col className={col}>
          <h6 className='fw-bold'>Material relevance</h6>
          {isDoubleMateriality
            ? [
                `Material score: ${data.relativeScore ?? ''}`,
                `Financial score: ${data.financialRelativeScore ?? ''}`,
                `Impact score: ${data.nonFinancialRelativeScore ?? ''}`,
              ].map((label, idx) => <Detail key={idx} label={label} />)
            : [`Priority: #${data.priority}`, `Relevance: ${relativeScore ?? ''}`, `Score: ${data.score}`].map(
                (label, idx) => <Detail key={idx} label={label} />,
              )}
        </Col>
        <Col className={col}>
          <h6 className='fw-bold'>Boundaries</h6>
          {boundary.map((code) => (
            <Detail key={code} {...boundaryMap[code]} />
          ))}
        </Col>
        <Col className={col}>
          <h6 className='fw-bold'>ESG relevance</h6>
          {esg.map((code) => (
            <Detail key={code} {...esgMap[code]} />
          ))}
        </Col>
        <Col className={col}>
          <h6 className='fw-bold'>Pillar</h6>
          {materialPillar.map((code) => (
            <Detail key={code} {...materialPillarMap[code]} />
          ))}
        </Col>
      </Row>
      {description ? (
        <>
          <h6 className='fw-bold mt-3'>Description</h6>
          <ExpandableParagraph defaultHeight={isCompact ? 80 : 44} key={`description-${data.code}`}>
            {description}
          </ExpandableParagraph>
        </>
      ) : null}
      {action ? (
        <>
          <h6 className='fw-bold mt-3'>Action</h6>
          <ExpandableParagraph defaultHeight={isCompact ? 164 : 86} key={`action-${data.code}`}>
            <GlossaryText text={action} glossary={glossaryState} />
          </ExpandableParagraph>
        </>
      ) : null}
    </div>
  );
};
