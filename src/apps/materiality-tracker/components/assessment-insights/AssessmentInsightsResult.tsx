import { MaterialTopicDetails } from '@apps/materiality-tracker/components/assessment-insights/MaterialTopicDetails';
import { DashboardSection } from '@g17eco/molecules/dashboard';
import { useCallback, useMemo, useState } from 'react';
import { AssessmentInsightsChart } from './AssessmentInsightsChart';
import { NoData } from '@g17eco/molecules/no-data';
import { isDoubleMaterialityAssessmentData } from '@apps/materiality-tracker/utils';
import { AssessmentInsightsDoubleMaterialityChart } from './AssessmentInsightsDoubleMaterialityChart';
import { BiDirectionalBarChart } from './BiDirectionalBarChart';
import { ChartSettings, PrioritizedAssessmentData, TopicsRange } from './types';
import { CategoriesCharts } from './CategoriesCharts';
import { ScatterChart } from './ScatterChart';
import { Option, SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { chartTypeOptions, categoryOptions, ChartType } from './chart-utils';

const filterDataWithRange = (data: PrioritizedAssessmentData[], range: TopicsRange) => {
  return data.slice(range.start - 1, range.end);
};

const getFormattedOption = <T = string>(value: T, options: Option<T>[], prefix: string) => {
  const selectedOption = options.find((op) => op.value === value);
  if (!selectedOption) {
    return null;
  }
  return {
    label: (
      <span>
        <strong className='mr-1'>{prefix}:</strong>
        {selectedOption.label}
      </span>
    ),
    value: selectedOption?.value,
  };
};

interface Props {
  data: PrioritizedAssessmentData[];
  chartSettings: ChartSettings;
  updateChartSettings: (settings: Partial<ChartSettings>) => void;
}

export const AssessmentInsightsResult = (props: Props) => {
  const { data, chartSettings, updateChartSettings } = props;
  const { topicsRange, chartType, category } = chartSettings;
  const filteredData = filterDataWithRange(data, topicsRange);
  const [selectedTopic, setSelectedTopic] = useState<PrioritizedAssessmentData | undefined>(filteredData[0]);
  const isDoubleMateriality = isDoubleMaterialityAssessmentData(data);

  const updateDataOnDataPointSelected = (selectedColumnIndex: number) => {
    setSelectedTopic(filteredData[selectedColumnIndex]);
  };

  if (data.length === 0) {
    return (
      <DashboardSection>
        <NoData text='No assessment results currently available.' />
      </DashboardSection>
    );
  }

  const selectedTopicCode = selectedTopic?.code ?? '';

  const renderChart = () => {
    switch (chartType) {
      case ChartType.ScatterChart:
        return isDoubleMaterialityAssessmentData(filteredData) ? (
          <ScatterChart
            data={filteredData}
            category={category}
            selectedTopicCode={selectedTopicCode}
            clickHandler={updateDataOnDataPointSelected}
          />
        ) : null;
      case ChartType.BiDirectionalBarChart:
        return isDoubleMaterialityAssessmentData(filteredData) ? (
          <BiDirectionalBarChart
            data={filteredData}
            category={category}
            columnClickHandler={updateDataOnDataPointSelected}
          />
        ) : null;
      case ChartType.ColumnChart:
        return isDoubleMaterialityAssessmentData(filteredData) ? (
          <AssessmentInsightsDoubleMaterialityChart
            data={filteredData}
            category={category}
            selectedTopicCode={selectedTopicCode}
            columnClickHandler={updateDataOnDataPointSelected}
          />
        ) : (
          <AssessmentInsightsChart
            data={filteredData}
            category={category}
            selectedTopicCode={selectedTopicCode}
            columnClickHandler={updateDataOnDataPointSelected}
          />
        );
      default:
        return null;
    }
  };

  return (
    <DashboardSection>
      <CategoriesCharts data={filteredData} />
      <div className='divider'></div>
      <div className='d-flex gap-2'>
        {isDoubleMateriality ? (
          <SelectFactory<ChartType>
            selectType={SelectTypes.SingleSelect}
            options={chartTypeOptions}
            onChange={(option) => (option ? updateChartSettings({ chartType: option.value }) : null)}
            value={getFormattedOption<ChartType>(chartType, chartTypeOptions, 'Chart')}
          />
        ) : null}
        <SelectFactory
          selectType={SelectTypes.SingleSelect}
          options={categoryOptions}
          onChange={(option) => (option ? updateChartSettings({ category: option.value }) : null)}
          value={getFormattedOption(category, categoryOptions, 'Legend')}
        />
      </div>
      {renderChart()}
      {selectedTopic ? <MaterialTopicDetails data={selectedTopic} /> : null}
    </DashboardSection>
  );
};
