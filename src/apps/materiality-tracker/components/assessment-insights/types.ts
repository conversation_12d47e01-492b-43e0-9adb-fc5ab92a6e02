import type { AssessmentData } from '@apps/materiality-tracker/api/materiality-assessment';
import type { ChartType, LegendType } from './chart-utils';

export interface TopicsRange {
  start: number;
  end: number;
}

export type PrioritizedAssessmentData = AssessmentData & { priority: number; disabled?: boolean };

export interface ChartSettings {
  chartType: ChartType;
  category: LegendType;
  topicsRange: TopicsRange;
}
