import { describe, it, expect } from 'vitest';
import { normalizeByRelativeScore } from './utils';
import { createAssessmentData } from '@fixtures/materiality-data';

describe('normalizeByRelativeScore', () => {
  describe('normal cases', () => {
    it('should normalize multiple items correctly', () => {
      const data = [
        createAssessmentData({ code: 'item1', relativeScore: 80 }),
        createAssessmentData({ code: 'item2', relativeScore: 40 }),
        createAssessmentData({ code: 'item3', relativeScore: 20 }),
      ];

      const result = normalizeByRelativeScore(data);

      expect(result).toHaveLength(3);
      expect(result[0].normalizedScore).toBe(100);
      expect(result[1].normalizedScore).toBe(50);
      expect(result[2].normalizedScore).toBe(25);
    });

    it('should round normalized scores to 2 decimal places', () => {
      const data = [createAssessmentData({ relativeScore: 3 }), createAssessmentData({ relativeScore: 1 })];

      const result = normalizeByRelativeScore(data);

      expect(result[0].normalizedScore).toBe(100);
      expect(result[1].normalizedScore).toBe(33.33);
    });

    it('should preserve all original properties', () => {
      const originalData = createAssessmentData({
        code: 'test-code',
        name: 'Test Name',
        score: 75,
        relativeScore: 60,
        priority: 1,
        description: 'Test description',
      });

      const result = normalizeByRelativeScore([originalData]);

      expect(result[0]).toEqual({
        ...originalData,
        normalizedScore: 100,
      });
    });
  });

  describe('edge cases', () => {
    it('should return empty array for empty input', () => {
      const result = normalizeByRelativeScore([]);

      expect(result).toEqual([]);
    });

    it('should handle all items with relativeScore of 0', () => {
      const data = [createAssessmentData({ relativeScore: 0 }), createAssessmentData({ relativeScore: 0 })];

      const result = normalizeByRelativeScore(data);

      expect(result).toHaveLength(2);
      expect(result[0].normalizedScore).toBe(0);
      expect(result[1].normalizedScore).toBe(0);
    });

    it('should handle mixed items with some undefined relativeScores', () => {
      const data = [
        createAssessmentData({ relativeScore: 60 }),
        createAssessmentData({ relativeScore: undefined }),
        createAssessmentData({ relativeScore: 30 }),
      ];

      const result = normalizeByRelativeScore(data);

      expect(result).toHaveLength(3);
      expect(result[0].normalizedScore).toBe(100);
      expect(result[1].normalizedScore).toBeUndefined();
      expect(result[2].normalizedScore).toBe(50);
    });
  });
});
