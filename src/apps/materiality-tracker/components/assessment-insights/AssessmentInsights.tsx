import { useLazyGetBackgroundReportUrlQuery } from '@api/survey-background-report';
import Dashboard from '@g17eco/molecules/dashboard';
import { exportToExcel } from '@features/downloads/util/exportToExcel';
import { QueryWrapper } from '@g17eco/molecules/query/QueryWrapper';
import { ROUTES } from '@constants/routes';
import { NoData } from '@g17eco/molecules/no-data';
import { useSiteAlert } from '@hooks/useSiteAlert';
import { generateUrl } from '@routes/util';
import { DATE, formatDateUTC, isAfter } from '@utils/date';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { OnChangeValue } from 'react-select';
import { IN_PROGRESS_JOB_STATUSES, JobStatus } from '@g17eco/types/background-jobs';
import {
  AssessmentData,
  MaterialityAssessmentScope,
  MaterialitySurveyModelMinData,
  useGeneratePPTXReportQuery,
  useGenerateScoresQuery,
  useGetMappedUniversalTrackersQuery,
  useRegenerateScoresMutation,
  DoubleMaterialityAssessmentData,
  useGetAssessmentSizeQuery,
  useRegeneratePPTXReportMutation,
} from '../../api/materiality-assessment';
import {
  assessmentDataColumnMap,
  AssessmentDataRow,
  convertResult,
  isDoubleMaterialitySurvey,
} from '../../utils';
import { AssessmentDropdown } from '../assessment/AssessmentDropdown';
import { AssessmentInsightsOverview } from './AssessmentInsightsOverview';
import { AssessmentInsightsResult } from './AssessmentInsightsResult';
import { skipToken } from '@reduxjs/toolkit/query';
import { DetailedReportGenerator } from './assessment-reports/detailed-report-generator';
import { Document, Packer } from 'docx';
import { saveAs } from 'file-saver';
import { InitiativeData } from '@g17eco/types/initiative';
import { BlockingLoader } from '@g17eco/atoms/loader';
import AssessmentNotComplete from '@g17eco/images/assessment-not-complete.gif';
import { useAppSelector } from '@reducers/index';
import { isStaff, isUserManager } from '@selectors/user';
import { DEFAULT_TOPICS_RANGE, TopicsRangeInput } from './TopicsRangeInput';
import { ChartSettings, PrioritizedAssessmentData } from './types';
import { AssessmentResults } from './assessment-results/AssessmentResults';
import { roundTo } from '@utils/number';
import { DataSources, GenerateCharts } from './assessment-reports/charts/GenerateCharts';
import { DoubleMaterialityReportGenerator } from './assessment-reports/double-materiality-report-generator';
import { ReviewMetricGroupAlert } from './ReviewMetricGroupAlert';
import { useGetMetricGroupsQuery } from '@api/metric-groups';
import { BasicAlert } from '@g17eco/molecules/alert';
import { OverviewActions } from './OverviewActions';
import { extendCustomTopics } from './utils';
import config from '../../../../config';
import { Option } from '../../../../molecules/select/types';
import { DataSource } from '@features/report-output/common';
import { ChartType, LegendType } from './chart-utils';

interface Props {
  initiative: InitiativeData;
  selectedSurvey: MaterialitySurveyModelMinData;
  surveys: MaterialitySurveyModelMinData[];
}

const shouldContinuePolling = (status: JobStatus) => IN_PROGRESS_JOB_STATUSES.includes(status);

const POLLING_INTERVAL = 10000;

const OUTDATED_ASSESSMENT_DATE = config.materialityTracker.outdatedAssessmentDate;

const getAverageScore = ({
  financialScore,
  nonFinancialScore,
}: {
  financialScore: number | undefined;
  nonFinancialScore: number | undefined;
}) => {
  const validScores = [financialScore, nonFinancialScore].filter(score => score !== undefined);
  if (validScores.length === 0) {
    return 0;
  }
  return roundTo(validScores.reduce((a, b) => a + b, 0) / validScores.length);
};

const getInitialDataSourcesState = (): DataSources => ({
  doubleMaterialityTopics: {
    loaded: false,
  },
});

const mapToDoubleMaterialityData = ({
  financialData,
  nonFinancialData,
}: {
  financialData: AssessmentData[];
  nonFinancialData: AssessmentData[];
}): DoubleMaterialityAssessmentData[] => {
  const nonFinancialMap = new Map(nonFinancialData.map((data) => [data.code, data]));
  return financialData
    .map((topic) => {
      const nonFinancialScore = nonFinancialMap.get(topic.code)?.score;
      const nonFinancialRelativeScore = nonFinancialMap.get(topic.code)?.relativeScore;
      const financialScore = topic.score;
      const financialRelativeScore = topic.relativeScore;
      const avgRelativeScore = getAverageScore({
        financialScore: financialRelativeScore,
        nonFinancialScore: nonFinancialRelativeScore,
      });
      const avgScore = getAverageScore({
        financialScore,
        nonFinancialScore,
      });
      return {
        ...topic,
        score: avgScore,
        relativeScore: avgRelativeScore,
        financialScore,
        financialRelativeScore,
        nonFinancialScore,
        nonFinancialRelativeScore,
      };
    })
    .sort(
      (a, b) => {
        const sumA = (a.financialRelativeScore ?? 0) + (a.nonFinancialRelativeScore ?? 0);
        const sumB = (b.financialRelativeScore ?? 0) + (b.nonFinancialRelativeScore ?? 0);
        return sumB - sumA;
      }
    ).map((topic, index) => ({
      ...topic,
      priority: index + 1,
    }));
};

export const AssessmentInsights = (props: Props) => {
  const { initiative, selectedSurvey, surveys } = props;
  const isDoubleMateriality = isDoubleMaterialitySurvey(selectedSurvey);
  const isStaffUser = useAppSelector(isStaff);
  const isAdmin = useAppSelector(isUserManager);
  const isSurveyOutdated = !isAfter(OUTDATED_ASSESSMENT_DATE, selectedSurvey.created);
  const canGenerate = Boolean(selectedSurvey.completedDate) && !isSurveyOutdated;

  const [chartSettings, setChartSettings] = useState<ChartSettings>({
    chartType: isDoubleMateriality ? ChartType.ScatterChart : ChartType.ColumnChart,
    category: LegendType.Materiality,
    topicsRange: DEFAULT_TOPICS_RANGE,
  });
  const [isEditing, setIsEditing] = useState(false);
  const generateScoreQuery = useGenerateScoresQuery(
    canGenerate ? { initiativeId: initiative._id, surveyId: selectedSurvey._id } : skipToken,
  );
  const { result, jobId: scoreJobId, status: scoreJobStatus, config, updatedAt } = generateScoreQuery.data ?? {};
  const hasFinishedScores = scoreJobId && scoreJobStatus && !shouldContinuePolling(scoreJobStatus);
  const { data: mappedUtrs = [], isLoading } = useGetMappedUniversalTrackersQuery(
    hasFinishedScores
      ? {
          initiativeId: initiative._id,
          surveyId: selectedSurvey._id,
          jobId: scoreJobId,
        }
      : skipToken,
  );

  const [regenerateScoreQuery] = useRegenerateScoresMutation();
  const generatePPTXReportQuery = useGeneratePPTXReportQuery(
    // Only fetch the report if the scores have finished generating
    hasFinishedScores
      ? { jobId: scoreJobId, initiativeId: initiative._id, surveyId: selectedSurvey._id }
      : skipToken,
  );
  const [regeneratePPTXReportQuery] = useRegeneratePPTXReportMutation();
  const [getBackgroundReportUrl] = useLazyGetBackgroundReportUrlQuery();
  const { data: assessmentSize, isLoading: isLoadingAssessmentSize } = useGetAssessmentSizeQuery({
    initiativeId: initiative._id,
    assessmentId: selectedSurvey._id,
  });

  const metricGroupQuery = useGetMetricGroupsQuery(initiative._id);

  const history = useHistory();
  const { addSiteError } = useSiteAlert();
  const [dataSourcesState, setData] = useState(getInitialDataSourcesState());

  const {
    status: pptxReportJobStatus,
    jobId: pptxReportJobId,
    taskId: pptxReportTaskId,
    completedDate,
  } = generatePPTXReportQuery.data ?? {};

  const hasFinishedPPTXReport = pptxReportJobStatus && !shouldContinuePolling(pptxReportJobStatus);
  const isPPTXReportOutdated = updatedAt && completedDate && new Date(updatedAt) > new Date(completedDate);
  const updateChartSettings = (settings: Partial<ChartSettings>) => {
    setChartSettings((prev) => ({ ...prev, ...settings }));
  };

  useEffect(() => {
    // Prevent doing unnecessary polling if survey is not completed
    if (!canGenerate) {
      return;
    }
    // Define a function to determine if polling should continue
    if (!hasFinishedScores) {
      const intervalId = setInterval(() => {
        generateScoreQuery.refetch();
        metricGroupQuery.refetch();
      }, POLLING_INTERVAL);

      return () => clearInterval(intervalId);
    }
  }, [canGenerate, generateScoreQuery, hasFinishedScores, metricGroupQuery]);

  useEffect(() => {
    // Prevent doing unnecessary polling if survey is not completed
    if (!canGenerate) {
      return;
    }
    // Define a function to determine if polling should continue
    if (!hasFinishedPPTXReport && !generatePPTXReportQuery.isUninitialized) {
      const intervalId = setInterval(() => {
        generatePPTXReportQuery.refetch();
      }, POLLING_INTERVAL);
      return () => clearInterval(intervalId);
    }
  }, [canGenerate, hasFinishedPPTXReport, generatePPTXReportQuery]);

  const onDownload = useCallback(
    (downloadUrl: string | undefined) => {
      if (downloadUrl) {
        window.open(downloadUrl, '_blank', '');
        return;
      }
      addSiteError({ content: 'Unable to download report. If this occurs again, please generate a new report.' });
    },
    [addSiteError],
  );

  const handlePPTXDownload = ({ jobId, taskId }: { jobId?: string | null; taskId?: string | null }) => {
    if (!jobId || !taskId || !scoreJobId) {
      return;
    }

    if (isPPTXReportOutdated) {
      regeneratePPTXReportQuery({ scoreJobId, initiativeId: initiative._id, surveyId: selectedSurvey._id });
      return;
    }

    getBackgroundReportUrl({ surveyId: selectedSurvey._id, jobId, taskId })
      .then((response) => onDownload(response.data?.downloadUrl))
      .catch(e => addSiteError(e))
  };

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const jobId = searchParams.get('jobId');
    const taskId = searchParams.get('taskId');
    if (!jobId || !taskId) {
      return;
    }
    history.replace(location.pathname);
    getBackgroundReportUrl({ surveyId: selectedSurvey._id, jobId, taskId })
      .then((response) => onDownload(response.data?.downloadUrl))
      .catch((e) => addSiteError(e));
  }, [addSiteError, getBackgroundReportUrl, history, onDownload, selectedSurvey._id]);

  const originalTopics: PrioritizedAssessmentData[] = useMemo(() => {
    if (!result) {
      return [];
    }
    const sortedTopics = result.financial
      .slice()
      .sort((a, b) => b.score - a.score)
      .map((topic, index) => ({
        ...topic,
        priority: index + 1,
      }));
    if (isDoubleMateriality) {
      return mapToDoubleMaterialityData({ financialData: sortedTopics, nonFinancialData: result.nonFinancial });
    }
    return sortedTopics;
  }, [isDoubleMateriality, result]);

  const customTopics = useMemo(() => {
    const orderedTopics = config?.orderedTopics;
    if (!orderedTopics) {
      return originalTopics;
    }
    return extendCustomTopics(originalTopics, orderedTopics);
  }, [config?.orderedTopics, originalTopics]);

  const filteredTopics = useMemo(() => {
    return customTopics.filter((topic) => !topic.disabled);
  }, [customTopics]);

  const regenerateScores = () => regenerateScoreQuery({ initiativeId: initiative._id, surveyId: selectedSurvey._id });

  const handleExcelDownload = () => {
    const rowHeaders = Object.values(assessmentDataColumnMap);
    const convertedResult = convertResult(filteredTopics);
    const rowValues = convertedResult.map((row: AssessmentDataRow) => {
      return Object.keys(assessmentDataColumnMap).map((key) => row[key as keyof AssessmentDataRow] ?? '');
    });

    exportToExcel({
      headers: rowHeaders,
      values: rowValues,
      fileName: `${
        selectedSurvey.name ?? formatDateUTC(selectedSurvey.effectiveDate, DATE.YEAR_ONLY)
      } Assessment Results`,
      sheetName: 'assessment-results',
    });
  };

  const handleDocxDownload = async () => {
    if (!result || isLoadingAssessmentSize) {
      return;
    }

    const sizeScope = assessmentSize?.sizeScope ?? MaterialityAssessmentScope.Solopreneur;
    let doc: Document;

    if (isDoubleMateriality) {
      doc = await DoubleMaterialityReportGenerator({
        survey: selectedSurvey,
        doubleMaterialityResult: filteredTopics as DoubleMaterialityAssessmentData[],
        initiative,
        mappedUtrs,
        sizeScope,
        dataSource: dataSourcesState.doubleMaterialityTopics,
      });
    } else {
      doc = await DetailedReportGenerator({
        survey: selectedSurvey,
        financialResult: filteredTopics,
        initiative,
        mappedUtrs,
        sizeScope,
      });
    }

    const type = isDoubleMateriality ? 'Double Materiality' : 'Financial';
    return Packer.toBlob(doc).then((blob) => {
      saveAs(
        blob,
        `${
          selectedSurvey.name ?? formatDateUTC(selectedSurvey.effectiveDate, DATE.YEAR_ONLY)
        } ${type} Assessment Results.docx`
      );
    });
  };

  const onClickItem = (option: OnChangeValue<Option<string>, false>) => {
    if (option?.value === selectedSurvey._id) {
      return;
    }
    history.push(
      generateUrl(ROUTES.MATERIALITY_TRACKER_INSIGHTS, {
        initiativeId: initiative._id,
        insightSurveyId: option?.value,
      }),
    );
  };

  const updateData = useCallback((key: string, data: DataSource) => {
    setData((dataSources) => ({
      ...dataSources,
      [key]: data,
    }));
  }, [setData]);

  return (
    <Dashboard className='assessmentContainer'>
      {canGenerate ? (
        <QueryWrapper
          query={generateScoreQuery}
          onError={() => <NoData text='There was an error fetching your results. Please try again.' />}
          onNoData={() => <NoData text='No assessment results currently available.' />}
          onSuccess={() => {
            return isEditing ? (
              <AssessmentResults
                initiativeId={initiative._id}
                surveyId={selectedSurvey._id}
                onClickBack={() => setIsEditing(false)}
                originalTopics={originalTopics}
                customTopics={customTopics}
                hasCustomTopics={!!config?.orderedTopics}
              />
            ) : (
              <>
                {isLoading ||
                isLoadingAssessmentSize ||
                (isDoubleMateriality && !dataSourcesState.doubleMaterialityTopics.loaded) ? (
                  <BlockingLoader />
                ) : null}
                <div className='d-flex px-3 mb-3 align-items-center justify-content-between'>
                  <AssessmentDropdown selectedSurvey={selectedSurvey} surveys={surveys} onClickItem={onClickItem} />
                  <TopicsRangeInput
                    key={filteredTopics.length}
                    topicsLength={filteredTopics.length}
                    onTopicsRangeChange={(topicsRange) => updateChartSettings({ topicsRange })}
                  />
                </div>
                <AssessmentInsightsOverview
                  pptxReportJob={generatePPTXReportQuery.data}
                  scoreJob={generateScoreQuery.data}
                  onExcelDownload={handleExcelDownload}
                  onPPTXDownload={() => handlePPTXDownload({ jobId: pptxReportJobId, taskId: pptxReportTaskId })}
                  onDocxDownload={handleDocxDownload}
                  regenerateScores={regenerateScores}
                  onEditResults={() => setIsEditing(true)}
                  isStaff={isStaffUser}
                  assessmentType={selectedSurvey.assessmentType}
                />
                {hasFinishedScores && isStaffUser && isAdmin ? (
                  <ReviewMetricGroupAlert
                    initiativeId={initiative._id}
                    assessmentId={selectedSurvey._id}
                    metricGroups={metricGroupQuery.data}
                  />
                ) : null}
                {isDoubleMateriality ? (
                  <GenerateCharts
                    updateData={updateData}
                    data={filteredTopics as DoubleMaterialityAssessmentData[]}
                    sizeScope={assessmentSize?.sizeScope ?? MaterialityAssessmentScope.Solopreneur}
                  />
                ) : null}
                {scoreJobStatus === JobStatus.Completed ? (
                  <AssessmentInsightsResult
                    key={`${JSON.stringify(chartSettings.topicsRange)}-${selectedSurvey._id}`}
                    chartSettings={chartSettings}
                    updateChartSettings={updateChartSettings}
                    data={filteredTopics}
                  />
                ) : null}
              </>
            );
          }}
        />
      ) : (
        <>
          <div className='d-flex pl-3 mb-3'>
            <AssessmentDropdown selectedSurvey={selectedSurvey} surveys={surveys} onClickItem={onClickItem} />
          </div>
          {isSurveyOutdated ? (
            <div>
              <BasicAlert type='primary' className='d-flex align-items-center mx-3'>
                <i className='fas fa-info-circle mr-2 text-lg' />
                Results for assessments created before {formatDateUTC(OUTDATED_ASSESSMENT_DATE)} are not available here,
                as reporting was handled manually during that time.
              </BasicAlert>
              <OverviewActions isStaff={isStaffUser} readOnly={true} />
            </div>
          ) : (
            <div className='text-center'>
              <h1 className='text-ThemeTextPlaceholder'>Complete assessment to view insights</h1>
              <img src={AssessmentNotComplete} alt='assessment not complete' />
            </div>
          )}
        </>
      )}
    </Dashboard>
  );
};
