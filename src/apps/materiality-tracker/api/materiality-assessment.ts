/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { createApi } from '@reduxjs/toolkit/query/react';
import { ApiResponse } from '../../../types/api';
import { axiosBaseQuery } from '../../../api/axiosQuery';
import { JobStatus } from '../../../types/background-jobs';
import { DateString } from '@utils/date';
import { UniversalTrackerBase, UniversalTrackerBlueprintMin } from '@g17eco/types/universalTracker';
import { PrioritizedAssessmentData } from '../components/assessment-insights/types';
import { AssessmentType, SurveyModelMinData, SurveyModelMinimalUtrv } from '@g17eco/types/survey';
import { SurveyActionData } from '@g17eco/types/surveyAction';
import { SurveyWorkgroup } from '@g17eco/types/workgroup';

const transformResponse = <T>(response: ApiResponse<T>) => response.data;
const transformErrorResponse = <T>(response: { error: any, message: string; name: string; }) => response;

export interface UtrMapping {
  code: string;
  name?: string;
  score: number;
}

export interface MaterialTopicCategories {
  esg?: ESGCategory[];
  sdg?: string[];
  materialPillar?: MaterialPillar[];
  boundary?: MaterialityBoundary[];
}

export enum ESGCategory {
  Environmental = 'Environmental',
  Social = 'Social',
  Governance = 'Governance',
}

export enum MaterialPillar {
  People = 'people',
  Partnership = 'partnership',
  Planet = 'planet',
  Prosperity = 'prosperity',
  Principle = 'principle',
}

export enum MaterialityBoundary {
  Leadership = 'leadership',
  ResearchAndDevelopment = 'research-and-development',
  SupplyChain = 'supply-chain',
  ProductAndServices = 'product-and-services',
  Distribution = 'distribution',
  Communities = 'communities',
  Experiences = 'experiences',
}

export enum MaterialityAssessmentScope {
  Startup = 'startup',
  Solopreneur = 'solopreneur',
  Micro = 'micro',
  SME = 'sme',
  MidCap = 'mid-cap',
  MNC = 'mnc',
  Large = 'large',
}

export interface AssessmentJobResult {
  jobId: string;
  status: JobStatus;
  result?: AssessmentResult;
  config?: MaterialityAssessmentConfig;
  updatedAt: string;
}

export interface PPTXReportJobResult {
  status: JobStatus;
  jobId: string;
  taskId: string;
  completedDate: string | undefined;
}

export interface AssessmentResult {
  financial: AssessmentData[];
  nonFinancial: AssessmentData[];
}

export type AssessmentData = {
  code: string;
  name?: string;
  score: number;
  relativeScore?: number;
  utrMapping?: UtrMapping[];
  categories?: MaterialTopicCategories;
  description?: string;
  action?: string;
}

export type DoubleMaterialityAssessmentData = PrioritizedAssessmentData & {
  financialScore: number | undefined;
  nonFinancialScore: number | undefined;
  financialRelativeScore: number | undefined;
  nonFinancialRelativeScore: number | undefined;
}

export type OrderedTopic = Pick<AssessmentData, 'code'> & {
  disabled?: boolean;
};

export interface MaterialityAssessmentConfig {
  orderedTopics: OrderedTopic[];
  explanation: string;
}

export enum UpdateImpactScope {
  Reports = 'reports',
  ModuleMetrics = 'module-metrics',
}

export interface MaterialitySurveyModelMinData extends SurveyModelMinData {
  fragmentUniversalTrackerValues: SurveyModelMinimalUtrv[];
  /** @todo: apply migration for materiality surveys then change this to required */
  assessmentType?: AssessmentType;
  workgroups: SurveyWorkgroup[]; // Not deal with workgroups for MT yet. Empty for now.
}
export interface CreateSurveyContext {
  utrs: UniversalTrackerBase[];
}
export interface CreateSurvey {
  initiativeId: string;
  effectiveDate: DateString;
  answers: {
    [key: string]: string | undefined;
  };
  returnUrl: string;
  referralCode?: string;
  assessmentType: AssessmentType;
  verificationRequired: boolean;
}

export enum Tag {
  Scores = 'scores',
  Survey = 'survey',
  SurveyList = 'survey-list',
  BuySurveyContext = 'buy-survey-context',
  PPTXReport = 'mt-pptx-report',
}

export const materialityAssessmentApi = createApi({
  reducerPath: 'materialityAssessmentApi',
  baseQuery: axiosBaseQuery(),
  tagTypes: Object.values(Tag),
  endpoints: (builder) => ({
    generateScores: builder.query<AssessmentJobResult, { initiativeId: string; surveyId: string }>({
      transformResponse,
      query: ({ initiativeId, surveyId }) => ({
        url: `/materiality-assessment/initiatives/${initiativeId}/surveys/${surveyId}/scores`,
        method: 'get',
      }),
      providesTags: [Tag.Scores],
    }),
    regenerateScores: builder.mutation<AssessmentJobResult, { initiativeId: string; surveyId: string }>({
      transformResponse,
      query: ({ initiativeId, surveyId }) => ({
        url: `/materiality-assessment/initiatives/${initiativeId}/surveys/${surveyId}/scores`,
        method: 'post',
      }),
      invalidatesTags: [Tag.Scores],
    }),
    getSurveys: builder.query<MaterialitySurveyModelMinData[], { initiativeId: string }>({
      transformResponse: (response: ApiResponse<MaterialitySurveyModelMinData[]>) => response.data.map((survey) => ({ ...survey, workgroups: []} )), // Not deal with workgroups for MT yet.
      query: ({ initiativeId }) => ({
        url: `/materiality-assessment/initiatives/${initiativeId}/surveys`,
        method: 'get',
      }),
      providesTags: [Tag.SurveyList],
    }),
    getBuySurveyContext: builder.query<CreateSurveyContext, { initiativeId: string }>({
      transformResponse,
      keepUnusedDataFor: 1,
      query: ({ initiativeId }) => ({
        url: `/materiality-assessment/initiatives/${initiativeId}/context`,
        method: 'get',
      }),
      providesTags: [Tag.BuySurveyContext],
    }),
    createSurvey: builder.mutation<{ sessionUrl?: string }, CreateSurvey>({
      transformResponse,
      transformErrorResponse,
      query: (data) => ({
        url: `/materiality-assessment/initiatives/${data.initiativeId}/create`,
        method: 'post',
        data,
      }),
      invalidatesTags: [Tag.SurveyList, Tag.BuySurveyContext],
    }),
    getSurvey: builder.query<SurveyActionData, { surveyId: string }>({
      transformResponse: (response: ApiResponse<SurveyActionData>) => ({ ...response.data, workgroups: []} ), // Not deal with workgroups for MT yet.
      query: ({ surveyId }) => ({
        url: `/surveys/${surveyId}`,
        method: 'get',
      }),
      providesTags: (_result, _error, arg) => {
        return [{ type: Tag.Survey, id: arg.surveyId }];
      },
    }),
    getSurveyCreateStatus: builder.query<
      { status: string; surveyId: string },
      { initiativeId: string; sessionId: string }
    >({
      transformResponse,
      query: (data) => ({
        url: `/materiality-assessment/initiatives/${data.initiativeId}/create/${data.sessionId}`,
        method: 'get',
      }),
    }),
    deleteSurvey: builder.mutation<null, { surveyId: string }>({
      transformResponse,
      query: ({ surveyId }) => ({
        url: `/surveys/${surveyId}`,
        method: 'delete',
      }),
      invalidatesTags: [Tag.SurveyList, Tag.Survey, Tag.BuySurveyContext],
    }),
    generatePPTXReport: builder.query<
      PPTXReportJobResult,
      { initiativeId: string; surveyId: string; jobId: string }
    >({
      transformResponse,
      query: ({ initiativeId, surveyId, jobId }) => ({
        url: `/materiality-assessment/initiatives/${initiativeId}/surveys/${surveyId}/scores/${jobId}/report/pptx`,
        method: 'get',
      }),
      providesTags: [Tag.PPTXReport],
    }),
    regeneratePPTXReport: builder.mutation<
      PPTXReportJobResult,
      { initiativeId: string; surveyId: string; scoreJobId: string }
    >({
      transformResponse,
      query: ({ initiativeId, surveyId, scoreJobId }) => ({
        url: `/materiality-assessment/initiatives/${initiativeId}/surveys/${surveyId}/scores/${scoreJobId}/report/pptx`,
        method: 'post',
      }),
      invalidatesTags: [Tag.PPTXReport],
    }),
    getAssessmentSize: builder.query<
      { sizeScope: MaterialityAssessmentScope },
      { initiativeId: string; assessmentId: string }
    >({
      transformResponse,
      query: ({ initiativeId, assessmentId }) => ({
        url: `/materiality-assessment/initiatives/${initiativeId}/surveys/${assessmentId}/size-scope`,
        method: 'get',
      }),
    }),
    getMappedUniversalTrackers: builder.query<
      UniversalTrackerBlueprintMin[],
      { initiativeId: string; surveyId: string; jobId: string }
    >({
      transformResponse,
      query: ({ initiativeId, surveyId, jobId }) => ({
        url: `/materiality-assessment/initiatives/${initiativeId}/surveys/${surveyId}/scores/${jobId}/questions`,
        method: 'get',
      }),
      providesTags: [Tag.Scores, Tag.Survey],
    }),
    updateConfig: builder.mutation<
      void,
      { initiativeId: string; surveyId: string; config: MaterialityAssessmentConfig; impactScopes: UpdateImpactScope[] }
    >({
      transformResponse,
      query: ({ initiativeId, surveyId, config, impactScopes }) => ({
        url: `/materiality-assessment/initiatives/${initiativeId}/surveys/${surveyId}/config`,
        method: 'put',
        data: { config, impactScopes },
      }),
      invalidatesTags: [Tag.Scores, Tag.PPTXReport],
    }),
    deleteConfig: builder.mutation<void, { initiativeId: string; surveyId: string; impactScopes: UpdateImpactScope[] }>({
      transformResponse,
      query: ({ initiativeId, surveyId, impactScopes }) => ({
        url: `/materiality-assessment/initiatives/${initiativeId}/surveys/${surveyId}/config`,
        method: 'delete',
        data: { impactScopes },
      }),
      invalidatesTags: [Tag.Scores, Tag.PPTXReport],
    }),
    // only used in MT onboarding flow
    canCreateFreeAssessment: builder.query<{ canCreateFreeAssessment: boolean }, string>({
      transformResponse,
      query: (initiativeId) => ({
        url: `/materiality-assessment/initiatives/${initiativeId}/free-assessment`,
        method: 'get',
      }),
    }),
  }),
});

export const {
  useGenerateScoresQuery,
  useRegenerateScoresMutation,
  useGetSurveysQuery,
  useLazyGetSurveyCreateStatusQuery,
  useLazyGetSurveysQuery,
  useCreateSurveyMutation,
  useGetSurveyQuery,
  useGetBuySurveyContextQuery,
  useDeleteSurveyMutation,
  useGeneratePPTXReportQuery,
  useLazyGetAssessmentSizeQuery,
  useGetAssessmentSizeQuery,
  useGetMappedUniversalTrackersQuery,
  useUpdateConfigMutation,
  useDeleteConfigMutation,
  useRegeneratePPTXReportMutation,
  useCanCreateFreeAssessmentQuery,
} = materialityAssessmentApi;
