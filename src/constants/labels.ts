/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import { QUESTION } from './terminology';

export const UTR = {
  NOTE: 'Further explanation / notes',
  FILES: 'Supporting Files',
};

export const TOOLTIP = {
  notReporting: `Marking a ${QUESTION.SINGULAR} as 'Not Reporting' will permanently record this in the provenance, which cannot be undone. To update the ${QUESTION.SINGULAR} in the future, you can provide a new answer.`,
  shareData: {
    restriction: 'This may be because you were referred or your subscription is subsidised',
  },
  privacy: `Mark ${QUESTION.SINGULAR} as 'Private' when you want the ${QUESTION.SINGULAR} to be hidden from external stakeholders.`,
  comment:
    'Comments are internal only and will not appear on your download reports. If you have an assurer, they will be able to see the comments and interact with you via the comments.',
};
