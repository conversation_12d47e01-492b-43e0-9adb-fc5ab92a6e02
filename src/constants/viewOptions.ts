/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { PACK, QUESTION, SURVEY } from '@constants/terminology';
import { ViewValues } from '@g17eco/types/surveyOverview';

export const ViewMap: { [index in ViewValues]: { value: ViewValues, label: string } } = {
  [ViewValues.SdgTarget]: {
    value: ViewValues.Sdg,
    label: 'Sustainable Development Goal Targets'
  },
  [ViewValues.Group]: {
    value: ViewValues.Group,
    label: 'Group',
  },
  [ViewValues.Sdg]: {
    value: ViewValues.Sdg,
    label: 'Sustainable Development Goals'
  },
  [ViewValues.Materiality]: {
    value: ViewValues.Materiality,
    label: 'Materiality'
  },
  [ViewValues.StandardsAndFrameworks]: {
    value: ViewValues.StandardsAndFrameworks,
    label: 'Standards and Frameworks'
  },
  [ViewValues.Standards]: {
    value: ViewValues.Standards,
    label: 'Standards'
  },
  [ViewValues.Frameworks]: {
    value: ViewValues.Frameworks,
    label: 'Frameworks'
  },
  [ViewValues.Regulatory]: {
    value: ViewValues.Regulatory,
    label: 'Regulatory'
  },
  [ViewValues.Ratings]: {
    value: ViewValues.Ratings,
    label: 'Ratings'
  },
  [ViewValues.Roles]: {
    value: ViewValues.Roles,
    label: 'Roles'
  },
  [ViewValues.Users]: {
    value: ViewValues.Users,
    label: 'Users'
  },
  [ViewValues.Workgroups]: {
    value: ViewValues.Workgroups,
    label: 'Workgroups'
  },
  [ViewValues.QuestionList]: {
    value: ViewValues.QuestionList,
    label: QUESTION.CAPITALIZED_PLURAL
  },
  [ViewValues.StandardsQuestionList]: {
    value: ViewValues.StandardsQuestionList,
    label: `Standards ${QUESTION.PLURAL}`
  },
  [ViewValues.Custom]: {
    value: ViewValues.Custom,
    label: `Custom ${PACK.CAPITALIZED_PLURAL}`
  },
  [ViewValues.QuestionPacks]: {
    value: ViewValues.QuestionPacks,
    label: `Core ${SURVEY.ADJECTIVE} ${PACK.PLURAL}`
  },
  [ViewValues.AssignedMetrics]: {
    value: ViewValues.AssignedMetrics,
    label: `Assigned ${PACK.CAPITALIZED_PLURAL}`
  },
  [ViewValues.AllPacks]: {
    value: ViewValues.AllPacks,
    label: `All ${SURVEY.ADJECTIVE} ${PACK.PLURAL}`
  },
  [ViewValues.Archived]: {
    value: ViewValues.Archived,
    label: 'Archived'
  },
}

export const viewOptions = [
  ViewValues.Sdg,
  ViewValues.StandardsAndFrameworks,
  ViewValues.Regulatory,
  ViewValues.Ratings,
  ViewValues.Custom,
  ViewValues.Roles,
  ViewValues.Users,
  ViewValues.QuestionPacks,
  ViewValues.AssignedMetrics,
  ViewValues.AllPacks,
].map((k: ViewValues) => ViewMap[k]);

export type ViewScopeType = typeof viewOptions;
