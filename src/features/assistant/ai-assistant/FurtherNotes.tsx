import { useGetAIUtrvFurtherNotesDraftMutation, useGetAIUtrvFurtherNotesDraftRefineMutation } from '@api/ai';
import { CopyButton } from '@features/copy';
import { SurveyModelMinimalUtrv } from '@g17eco/types/survey';
import { useEffect, useMemo } from 'react';
import { QueryError } from '@g17eco/molecules/query/QueryError';
import { SectionLoader } from '@g17eco/atoms/loader';
import { AIRefineButton, HandleRefineData } from './AIRefineButton';

interface Props {
  utrv: SurveyModelMinimalUtrv;
}
export const FurtherNotes = ({ utrv }: Props) => {
  const [
    getFurtherNotesDraft,
    { isLoading: isGeneratingDraft, data: draftData, isError: isDraftError, error: draftError },
  ] = useGetAIUtrvFurtherNotesDraftMutation();
  const [
    getFurtherNotesDraftRefine,
    { isLoading: isRefining, data: refineData, isError: isRefineError, error: refineError },
  ] = useGetAIUtrvFurtherNotesDraftRefineMutation();

  const suggestedFurtherNotes = useMemo(() => {
    return refineData?.content ?? draftData?.content ?? '';
  }, [draftData, refineData]);

  useEffect(() => {
    getFurtherNotesDraft({ initiativeId: utrv.initiativeId, utrvId: utrv._id, draftData: utrv });
  }, [getFurtherNotesDraft, utrv]);

  const handleRefine = (data: HandleRefineData) => {
    getFurtherNotesDraftRefine({
      initiativeId: utrv.initiativeId,
      utrvId: utrv._id,
      draftData: utrv,
      action: data.action,
      additionalContext: data.additionalContext,
      textToRefine: suggestedFurtherNotes,
    });
  };

  const getError = () => {
    const error = isDraftError ? draftError : isRefineError ? refineError : null;
    return error ? <QueryError error={error} type={'danger'} /> : null;
  };

  return (
    <div>
      <div className='d-flex justify-content-between align-items-center'>
        <h6 className='my-0 text-ThemeHeadingDark'>AI Draft Further Explanation/Notes:</h6>
        <div className='flex-grow-0 d-flex gap-1 align-items-center'>
          <CopyButton content={suggestedFurtherNotes} />
          <AIRefineButton handleRefine={handleRefine} utrvId={utrv._id} />
        </div>
      </div>
      {getError()}
      {isGeneratingDraft || isRefining ? (
        <SectionLoader />
      ) : (
        <p className='text-ThemeTextMedium'>{suggestedFurtherNotes}</p>
      )}
    </div>
  );
};
