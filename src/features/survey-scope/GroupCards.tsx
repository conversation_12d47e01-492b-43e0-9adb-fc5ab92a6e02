/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CardGridGroup } from './CardGrid';
import { customMetric, getGroup, GroupTypes, Group } from '@g17eco/core';
import { CardProps } from '../../types/surveyScope';
import { ViewValues } from '@g17eco/types/surveyOverview';
import { filterStandardAndFramework } from '@utils/survey/filters';
import { getScopeInfo } from './scopeSelection';
import { BreadcrumbFilter, convertToFilters } from '@utils/survey/useScopeFilters';
import { findChildGroupByCode } from '../../utils/groups';
import { QUESTION } from '@constants/terminology';
import type { MetricGroup } from '../../types/metricGroup';
import { filterMetricGroups } from '../../utils/metricGroup';

interface Filters {
  type: string;
  value?: string;
}

const findCurrentGroup = <T extends Group | MetricGroup>(filters: Filters[], group?: T): T | undefined => {
  if (!group) {
    return;
  }

  const filterGroups = filters.filter((f) => [ViewValues.Group, ViewValues.CustomGroup].includes(f.type as ViewValues));
  let currentGroup = group;
  for (const filter of filterGroups) {
    if (!filter.value) {
      return;
    }
    currentGroup = findChildGroupByCode(group, filter.value) as T;
  }

  return currentGroup;
};

interface GroupConfig<T extends Group | MetricGroup> {
  getCode: (item: T) => string;
  getName: (item: T) => string;
  getIcon: (item: T, parentGroup?: T) => string | undefined;
  getDescription: (item: T) => string | undefined;
  filterQuestions: (questionList: CardProps['questionList'], item: T, parentGroup?: T) => CardProps['questionList'];
  scopeViewValue: ViewValues;
  drilldownViewValue: ViewValues;
  getGroup: (viewFilter: Required<BreadcrumbFilter>, props: CardProps) => T | undefined;
}

// Configuration for Group type
export const groupConfig: GroupConfig<Group> = {
  getCode: (item) => item.code,
  getName: (item) => item.name,
  getIcon: (item, parentGroup) => item.src ?? parentGroup?.src ?? customMetric.src,
  getDescription: (item) => item.description,
  filterQuestions: (questionList, item) => questionList.filter((q) => filterStandardAndFramework(q, item.code)),
  scopeViewValue: ViewValues.StandardsAndFrameworks,
  drilldownViewValue: ViewValues.Group,
  getGroup: (viewFilter) => {
    const filter =
      viewFilter.type === ViewValues.QuestionPacks || viewFilter.type === ViewValues.AllPacks
        ? ViewValues.StandardsAndFrameworks
        : viewFilter.type;
    return getGroup(filter as GroupTypes, viewFilter.value);
  },
};

// Configuration for MetricGroup type
export const metricGroupConfig: GroupConfig<MetricGroup> = {
  getCode: (item) => item._id,
  getName: (item) => item.groupName,
  getIcon: (item) => item.groupData?.icon,
  getDescription: (item) => item.description,
  filterQuestions: (questionList, item, parentGroup) =>
    questionList.filter((q) =>
      filterMetricGroups({
        utrId: q.universalTracker.getId(),
        metricGroupId: item._id,
        metricGroups: parentGroup?.subgroups,
      }),
    ),
  scopeViewValue: ViewValues.Custom,
  drilldownViewValue: ViewValues.CustomGroup,
  getGroup: (viewFilter, props) => {
    return props.metricGroups?.find((g) => g._id === viewFilter.value);
  },
};

export function getGroupCards<T extends Group | MetricGroup>(
  props: CardProps & {
    config: GroupConfig<T>;
  },
): CardGridGroup[] {
  const { config, questionList, addBtn, handleDrilldown, breadcrumbs, view, surveyData, materiality, metricGroups } =
    props;

  const cardGroups: CardGridGroup[] = [];

  const [viewFilter, ...groupFilters] = convertToFilters(view, breadcrumbs);

  if (!viewFilter.value) {
    return cardGroups;
  }

  const group = config.getGroup(viewFilter as Required<BreadcrumbFilter>, props);

  if (!group?.subgroups) {
    return cardGroups;
  }

  const currentSubGroup = findCurrentGroup(groupFilters, group);

  if (!currentSubGroup || !currentSubGroup.subgroups) {
    return cardGroups;
  }

  const cards: CardGridGroup = {
    cards: [],
  };

  const getButtons = (code: string, name: string) => {
    const subGroup = currentSubGroup.subgroups?.find((g) => config.getCode(g as T) === code);
    const hasSubGroups = subGroup && subGroup.subgroups && subGroup.subgroups.length > 0;
    const buttons = [];

    if (hasSubGroups) {
      buttons.push({
        icon: <i className='fa fa-list text-ThemeTextPlaceholder' />,
        tooltip: `${name} Groups - Click to view`,
        onClick: () => handleDrilldown(config.drilldownViewValue, code, name),
      });
    } else {
      buttons.push({
        icon: <i className='fa fa-list text-ThemeTextPlaceholder' />,
        tooltip: `${name} ${QUESTION.CAPITALIZED_PLURAL} - Click to view`,
        onClick: () => handleDrilldown(ViewValues.QuestionList, code, name),
      });
    }

    if (config.drilldownViewValue === ViewValues.Group) {
      buttons.push(addBtn(code, name, group as Group));
    } else {
      buttons.push(addBtn(code, name));
    }
    return buttons;
  }

  currentSubGroup.subgroups.forEach((sg) => {
    const subGroup = sg as T;
    const code = config.getCode(subGroup);
    const name = config.getName(subGroup);
    const description = config.getDescription(subGroup);

    const filteredQuestionList = config.filterQuestions(questionList, subGroup, currentSubGroup);

    if (filteredQuestionList.length === 0) {
      return;
    }

    const logoSrc = config.getIcon(subGroup, group);
    const scopeTag = code;

    const { inScope, isPartial } = getScopeInfo(surveyData.scope, scopeTag, config.scopeViewValue, breadcrumbs, {
      materiality,
      metricGroups,
    });
    cards.cards.push({
      key: `scope-cardgriditem-group-${scopeTag}`,
      title: <span>{name}</span>,
      sortTitle: name,
      description,
      icon: <img src={logoSrc} alt={name} width='40px' />,
      unitCount: filteredQuestionList.length,
      buttons: getButtons(code, name),
      scopeTag,
      inScope,
      isPartial,
    });
  });

  cardGroups.push(cards);
  return cardGroups;
}
