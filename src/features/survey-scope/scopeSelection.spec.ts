/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { getScopeInfo, processScopeData } from './scopeSelection';
import {
  getGroup,
  getGroupChildrenTags,
  getSubGroup,
  Group
} from '@g17eco/core';
import { SurveyScope } from '@g17eco/types/surveyCommon';
import { BreadcrumbFilter } from '@utils/survey/useScopeFilters';
import { Materiality } from '@g17eco/types/initiative';
import { Breadcrumb } from '@g17eco/types/surveyScope';
import { ViewValues } from '@g17eco/types/surveyOverview';
import { MetricGroup } from '../../types/metricGroup';
import { faker } from '@faker-js/faker';


interface ScopeData {
  scopeData: string[];
  value: string;
  scopeType: string;
  breadcrumbs?: Breadcrumb[];
}

interface ScopeInfoDataProvider extends ScopeData {

  expectInScope: boolean;
  expectIsPartial: boolean;
  partialScope?: Partial<SurveyScope>;
}

interface ScopeGroup {
  code: string,
  scopeType: 'standards' | 'frameworks' | 'custom' | 'sdg';
  scopeTags?: string[]
}

interface ScopeDataProvider {
  type: string;
  data: string[];
  filters: BreadcrumbFilter[];
  expectedScopeGroups: ScopeGroup[];
  expectedType?: string;
}

const createGroupBreadcrumb = (cardCategory: string) => ({
  cardCategory: cardCategory,
  cardGroup: ViewValues.Group,
  title: ''
});

//utility function
const createScope = (partialScope: Partial<SurveyScope>): SurveyScope => {
  return {
    sdg: [],
    materiality: [],
    standards: [],
    frameworks: [],
    custom: [],
    ...partialScope
  }
}

const materiality: any = [
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  100, //7
  100, //8
  66,
  33,
  0,
  0,
  0,
  100, //14
  100, //15
  0,
  0
]

describe('scopeSelection checks', () => {
  const sdgGroup = getGroup('sdg', '1')
  const allTargets = sdgGroup?.subgroups?.map(g => g.code) ?? [];
  describe('getScopeInfo Simple', () => {

    const griGroupOne = getGroup(ViewValues.Standards, 'gri') as Group;
    const subGroupOne = getSubGroup(griGroupOne, griGroupOne?.subgroups?.[0].code || '') as Group;
    const subChildGroupCodes = getGroupChildrenTags(subGroupOne)

    const middleCodes = griGroupOne?.subgroups?.map(g => g.code) ?? [];
    const griCodesWithoutMiddleCodes = getGroupChildrenTags(griGroupOne)
      .filter(c => !middleCodes.includes(c))

    const [issbGroupCode, issbFirstSubgroupCode, issbSecondSubgroupCode, issbThirdSubgroupCode] = [
      'issb',
      'ifrs_s1',
      'issb_strategy_s1',
      'issb_strategy_s1_ro',
    ];

    const data: ScopeInfoDataProvider[] = [
      {
        scopeData: ['1'],
        value: '1',
        scopeType: ViewValues.Sdg,
        expectInScope: true,
        expectIsPartial: false,
      },
      {
        scopeData: ['1', '1.1'],
        value: '1',
        scopeType: ViewValues.Sdg,
        expectInScope: true,
        expectIsPartial: false,
      },
      {
        scopeData: ['1.1', '1.2'],
        value: '1',
        scopeType: ViewValues.Sdg,
        expectInScope: false,
        expectIsPartial: true,
      },
      {
        scopeData: allTargets,
        value: '1',
        scopeType: ViewValues.Sdg,
        expectInScope: true,
        expectIsPartial: false,
      },
      {
        scopeData: ['1'],
        value: '1.1',
        scopeType: ViewValues.Sdg,
        expectInScope: true,
        expectIsPartial: false,
        breadcrumbs: [{ cardCategory: '1', cardGroup: ViewValues.SdgTarget, title: '' }]
      },
      {
        scopeData: subChildGroupCodes,
        value: '11312131-123123',
        scopeType: ViewValues.Standards,
        expectInScope: false,
        expectIsPartial: false,
        breadcrumbs: [
          createGroupBreadcrumb(griGroupOne.code),
          createGroupBreadcrumb(subGroupOne.code),
        ]
      },
      {
        scopeData: subChildGroupCodes,
        value: subGroupOne.code,
        scopeType: ViewValues.Standards,
        expectInScope: true,
        expectIsPartial: false,
        breadcrumbs: [
          createGroupBreadcrumb(griGroupOne.code),
        ]
      },
      {
        scopeData: griCodesWithoutMiddleCodes,
        value: griGroupOne.code,
        scopeType: ViewValues.Standards,
        expectInScope: true,
        expectIsPartial: false,
      },
      {
        scopeData: griCodesWithoutMiddleCodes.slice(0, griCodesWithoutMiddleCodes.length - 2),
        value: griGroupOne.code,
        scopeType: ViewValues.Standards,
        expectInScope: false,
        expectIsPartial: true,
      },
      {
        scopeData: subChildGroupCodes,
        value: subGroupOne.code,
        scopeType: ViewValues.Standards,
        expectInScope: true,
        expectIsPartial: false,
        breadcrumbs: [
          createGroupBreadcrumb(griGroupOne.code)
        ]
      },
      {
        scopeData: subChildGroupCodes,
        value: griGroupOne.code,
        scopeType: ViewValues.Standards,
        expectInScope: false,
        expectIsPartial: true,
        breadcrumbs: [
        ]
      },
      {
        scopeData: subChildGroupCodes.slice(0, 1),
        value: subGroupOne.code,
        scopeType: ViewValues.Standards,
        expectInScope: false,
        expectIsPartial: true,
        breadcrumbs: [
          createGroupBreadcrumb(griGroupOne.code),
          createGroupBreadcrumb(subGroupOne.code),
        ]
      },
      {
        scopeData: subChildGroupCodes,
        value: subChildGroupCodes[0],
        scopeType: ViewValues.Standards,
        expectInScope: true,
        expectIsPartial: false,
        breadcrumbs: [
          createGroupBreadcrumb(griGroupOne.code),
          createGroupBreadcrumb(subGroupOne.code),
        ]
      },
      {
        scopeData: subChildGroupCodes.filter(c => c !== subChildGroupCodes[0]),
        value: subChildGroupCodes[0],
        scopeType: ViewValues.Standards,
        expectInScope: false,
        expectIsPartial: false,
        breadcrumbs: [
          createGroupBreadcrumb(griGroupOne.code),
          createGroupBreadcrumb(subGroupOne.code),
        ]
      },
      // 3rd level of subgroup ISSB is added to survey's scope
      {
        // view scope page as all-packs
        scopeData: [issbThirdSubgroupCode],
        value: issbGroupCode,
        scopeType: ViewValues.Standards,
        expectInScope: false,
        expectIsPartial: true,
        breadcrumbs: [],
      },
      // view issb's subgroups (1st level of subgroups)
      {
        scopeData: [issbThirdSubgroupCode],
        value: issbFirstSubgroupCode,
        scopeType: ViewValues.Standards,
        expectInScope: false,
        expectIsPartial: true, // partially contains 3rd subgroup
        breadcrumbs: [createGroupBreadcrumb(issbGroupCode)],
      },
      {
        scopeData: [issbThirdSubgroupCode],
        value: 'ifrs_s2', // not in scope
        scopeType: ViewValues.Standards,
        expectInScope: false,
        expectIsPartial: false,
        breadcrumbs: [createGroupBreadcrumb(issbGroupCode)],
      },
      // view 2nd level of subgroups
      {
        scopeData: [issbThirdSubgroupCode],
        value: issbSecondSubgroupCode,
        scopeType: ViewValues.Standards,
        expectInScope: false,
        expectIsPartial: true, // partially contains 3rd subgroup
        breadcrumbs: [createGroupBreadcrumb(issbGroupCode), createGroupBreadcrumb(issbFirstSubgroupCode)],
      },
      // view 3rd level of subgroups
      {
        scopeData: [issbThirdSubgroupCode],
        value: issbThirdSubgroupCode,
        scopeType: ViewValues.Standards,
        expectInScope: true,
        expectIsPartial: false,
        breadcrumbs: [
          createGroupBreadcrumb(issbGroupCode),
          createGroupBreadcrumb(issbFirstSubgroupCode),
          createGroupBreadcrumb(issbSecondSubgroupCode),
        ],
      },
    ];

    data.forEach((provider) => {

      const {
        scopeType,
        scopeData,
        value,
        expectInScope,
        expectIsPartial,
        breadcrumbs = []
      } = provider;

      test(`Data [${scopeData}], Scope type ${scopeType}, value: ${value}`, () => {
        const scope = createScope({ [scopeType]: scopeData });
        const { inScope, isPartial } = getScopeInfo(scope, value, scopeType as ViewValues, breadcrumbs, {
          materiality,
        });
        expect(inScope).toBe(expectInScope);
        expect(isPartial).toBe(expectIsPartial);
      });
    });

    describe('metricGroups', () => {
      const createMetricGroup = (overrides: Partial<MetricGroup>): MetricGroup => ({
        _id: faker.database.mongodbObjectId(),
        code: 'metric-group-code',
        groupName: 'Metric Group',
        initiativeId: faker.database.mongodbObjectId(),
        updated: '2025-08-18T00:00:00.000Z',
        ...overrides,
      });

      const [groupId, childGroupIdOne, childGroupIdTwo, grandChildGroupIdOne, grandChildGroupIdTwo] = Array.from({
        length: 5,
      }).map(() => faker.database.mongodbObjectId()); 

      const metricGroup = createMetricGroup({
          _id: groupId,
          subgroups: [
            createMetricGroup({
              _id: childGroupIdOne,
              subgroups: [
                createMetricGroup({ _id: grandChildGroupIdOne, groupName: 'Grand Child Group One' }),
                createMetricGroup({ _id: grandChildGroupIdTwo, groupName: 'Grand Child Group Two' }),
              ],
            }),
            createMetricGroup({ _id: childGroupIdTwo, groupName: 'Child Group Two' }),
          ],
        })

      const metricGroups: MetricGroup[] = [metricGroup];

      it('metric group in scope', () => {
        const scope = {
          custom: [groupId],
        };
        const { inScope, isPartial } = getScopeInfo(scope, groupId, ViewValues.AllPacks, [], { metricGroups });
        expect(inScope).toBe(true);
        expect(isPartial).toBe(false);
      });

      it('metric group in scope (all sub groups in scope)', () => {
        const scope = {
          custom: [childGroupIdOne, childGroupIdTwo],
        };
        const { inScope, isPartial } = getScopeInfo(scope, groupId, ViewValues.AllPacks, [], { metricGroups });
        expect(inScope).toBe(true);
        expect(isPartial).toBe(false);
      });

      it('metric group partial', () => {
        const scope = {
          custom: [childGroupIdOne],
        };
        const { inScope, isPartial } = getScopeInfo(scope, groupId, ViewValues.AllPacks, [], { metricGroups });
        expect(inScope).toBe(false);
        expect(isPartial).toBe(true);
      });

      it('sub metric group in scope', () => {
        const scope = {
          custom: [childGroupIdOne],
        };
        const breadcrumbs = [{ cardCategory: groupId, cardGroup: ViewValues.CustomGroup, title: metricGroup.groupName }];
        const { inScope, isPartial } = getScopeInfo(scope, childGroupIdOne, ViewValues.CustomGroup, breadcrumbs, { metricGroups });
        expect(inScope).toBe(true);
        expect(isPartial).toBe(false);
      });

      it('sub metric group partial', () => {
        const scope = {
          custom: [grandChildGroupIdOne],
        };
        const breadcrumbs = [{ cardCategory: groupId, cardGroup: ViewValues.CustomGroup, title: metricGroup.groupName }];
        const { inScope, isPartial } = getScopeInfo(scope, childGroupIdOne, ViewValues.CustomGroup, breadcrumbs, { metricGroups });
        expect(inScope).toBe(false);
        expect(isPartial).toBe(true);
      });
    });
  });

  describe('materialityInfo', () => {

    const data: ScopeInfoDataProvider[] = [
      {
        scopeData: [],
        value: Materiality.Medium,
        scopeType: ViewValues.Materiality,
        expectInScope: true,
        expectIsPartial: false,
        breadcrumbs: [],
        partialScope: { sdg: ['9'] }
      },
      {
        scopeData: [],
        value: Materiality.Medium,
        scopeType: ViewValues.Materiality,
        expectInScope: false,
        expectIsPartial: false,
        breadcrumbs: [],
        partialScope: { sdg: [] }
      },
      {
        scopeData: [],
        value: Materiality.High,
        scopeType: ViewValues.Materiality,
        expectInScope: false,
        expectIsPartial: true,
        breadcrumbs: [],
        partialScope: { sdg: ['8.1', '14'] }
      },
      {
        scopeData: [],
        value: Materiality.High,
        scopeType: ViewValues.Materiality,
        expectInScope: false,
        expectIsPartial: true,
        breadcrumbs: [],
        partialScope: { sdg: ['8', '14'] }
      },
      {
        scopeData: [],
        value: Materiality.High,
        scopeType: ViewValues.Materiality,
        expectInScope: true,
        expectIsPartial: false,
        breadcrumbs: [],
        partialScope: { sdg: ['7', '8', '14', '15'] }
      },
      {
        scopeData: [],
        value: Materiality.High,
        scopeType: ViewValues.Materiality,
        expectInScope: false,
        expectIsPartial: false,
        breadcrumbs: [],
        partialScope: { sdg: ['3', '5'] }
      },
      {
        scopeData: [],
        value: Materiality.High,
        scopeType: ViewValues.Materiality,
        expectInScope: false,
        expectIsPartial: false,
        breadcrumbs: [],
        partialScope: { sdg: ['3.2', '5.5'] }
      },
      {
        scopeData: [],
        value: Materiality.High,
        scopeType: ViewValues.Materiality,
        expectInScope: false,
        expectIsPartial: true,
        breadcrumbs: [],
        partialScope: { sdg: ['7.2', '8.1'] }
      },
      {
        scopeData: [],
        value: Materiality.High,
        scopeType: ViewValues.Materiality,
        expectInScope: true,
        expectIsPartial: false,
        breadcrumbs: [],
        partialScope: { sdg: ['7.1', '7.2', '7.3', '8', '14', '15'] }
      },
      {
        scopeData: [],
        value: Materiality.High,
        scopeType: ViewValues.Materiality,
        expectInScope: false,
        expectIsPartial: true,
        breadcrumbs: [],
        partialScope: { sdg: ['7.1', '7.2', '8', '14', '15'] }
      },
      {
        scopeData: [],
        value: Materiality.None,
        scopeType: ViewValues.Materiality,
        expectInScope: false,
        expectIsPartial: false,
        breadcrumbs: [],
        partialScope: { sdg: [] }
      },
      {
        scopeData: [],
        value: Materiality.High,
        scopeType: ViewValues.AllPacks,
        expectInScope: false,
        expectIsPartial: true,
        breadcrumbs: [],
        partialScope: { sdg: ['7.1', '7.2', '8', '14', '15'] }
      },
      {
        scopeData: [],
        value: Materiality.None,
        scopeType: ViewValues.AllPacks,
        expectInScope: false,
        expectIsPartial: false,
        breadcrumbs: [],
        partialScope: { sdg: [] }
      },
    ]

    data.forEach((provider) => {

      const {
        scopeType,
        scopeData,
        value,
        expectInScope,
        expectIsPartial,
        breadcrumbs = [],
        partialScope
      } = provider;

      test(`Data [${scopeData}], Scope type ${scopeType}, value: ${value}`, () => {
        const scope = partialScope ? createScope(partialScope) : createScope({ [scopeType]: scopeData })
        const { inScope, isPartial } = getScopeInfo(scope, value, scopeType as ViewValues, breadcrumbs, { materiality })
        expect(inScope).toBe(expectInScope)
        expect(isPartial).toBe(expectIsPartial)
      })
    })

  })

  describe('getScopeInfo Assigned', () => {

    const data: Omit<ScopeInfoDataProvider, 'scopeType'>[] = [
      {
        scopeData: [
          '606d985bec43004a6ef350b5',
          '5ff47c94cf2b0121bb4f51dc',
          '5fda02fea3cab4fbda056171'
        ],
        value: '5fda02fea3cab4fbda056171',
        expectInScope: true,
        expectIsPartial: false,
      },
    ];

    const scopeType = ViewValues.AssignedMetrics;
    data.forEach((provider) => {
      const { scopeData, value, expectInScope, expectIsPartial, breadcrumbs = [] } = provider;

      test(`Data [${scopeData}], Scope type ${scopeType}, value: ${value}`, () => {
        const scope = createScope({ 'custom': scopeData })
        const { inScope, isPartial } = getScopeInfo(scope, value, scopeType, breadcrumbs, { materiality })
        expect(inScope).toBe(expectInScope)
        expect(isPartial).toBe(expectIsPartial)
      })
    })
  });

  describe('Virtual types', () => {

    const data: Omit<ScopeInfoDataProvider, 'scopeType'>[] = [
      {
        scopeData: ['1', '11'],
        value: '1',
        expectInScope: true,
        expectIsPartial: false,
      },
      {
        scopeData: ['11.1', '1.2', '2'],
        value: '11',
        expectInScope: false,
        expectIsPartial: true,
      },
      {
        scopeData: ['11.1', '1.2', '2'],
        partialScope: {
          frameworks: ['strategy'],
        },
        value: 'tcfd',
        expectInScope: false,
        expectIsPartial: true,
      },
      {
        scopeData: [],
        partialScope: {
          standards: ['gri-1'],
        },
        value: 'gri',
        expectInScope: false,
        expectIsPartial: true,
      },
      {
        scopeData: ['5f0c5cc20c96b744ace99019'],
        value: '5f0c5cc20c96b744ace99019',
        expectInScope: true,
        expectIsPartial: false,
      },
      {
        scopeData: [],
        partialScope: {
          frameworks: ['strategy'],
        },
        value: 'tcfd',
        expectInScope: false,
        expectIsPartial: true,
      },
    ];

    const scopeTypes = [ViewValues.AllPacks, ViewValues.QuestionPacks, ViewValues.Archived];
    for (const scopeType of scopeTypes) {
      data.forEach((provider) => {
        const { scopeData, value, expectInScope, expectIsPartial, breadcrumbs = [], partialScope } = provider;
        test(`Data [${scopeData}], Scope type ${scopeType}, value: ${value}`, () => {
          const scope = partialScope ? createScope(partialScope) : createScope({ 'sdg': scopeData })
          const { inScope, isPartial } = getScopeInfo(scope, value, scopeType, breadcrumbs, { materiality })
          expect(inScope).toBe(expectInScope)
          expect(isPartial).toBe(expectIsPartial)
        })
      })
    }

  });

  describe('processScopeData()', () => {

    const dataProvider: ScopeDataProvider[] = [
      {
        type: 'sdg',
        data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17'],
        filters: [],
        expectedScopeGroups: [
          {
            scopeType: 'sdg',
            code: '1'
          },
          {
            scopeType: 'sdg',
            code: '2'
          },
          {
            scopeType: 'sdg',
            code: '3'
          },
          {
            scopeType: 'sdg',
            code: '4'
          },
          {
            scopeType: 'sdg',
            code: '5'
          },
          {
            scopeType: 'sdg',
            code: '6'
          },
          {
            scopeType: 'sdg',
            code: '7'
          },
          {
            scopeType: 'sdg',
            code: '8'
          },
          {
            scopeType: 'sdg',
            code: '9'
          },
          {
            scopeType: 'sdg',
            code: '10'
          },
          {
            scopeType: 'sdg',
            code: '11'
          },
          {
            scopeType: 'sdg',
            code: '12'
          },
          {
            scopeType: 'sdg',
            code: '13'
          },
          {
            scopeType: 'sdg',
            code: '14'
          },
          {
            scopeType: 'sdg',
            code: '15'
          },
          {
            scopeType: 'sdg',
            code: '16'
          },
          {
            scopeType: 'sdg',
            code: '17'
          }
        ]
      },
      {
        type: 'sdg',
        data: ['1'],
        filters: [],
        expectedScopeGroups: [{ scopeType: 'sdg', code: '1' }]
      },
      {
        type: 'sdg',
        data: ['1.2'],
        filters: [{ 'type': 'sdg', 'value': '1' }],
        expectedScopeGroups: [{ scopeType: 'sdg', code: '1', scopeTags: ['1.2'] }]
      },
      {
        type: 'standards',
        data: ['aichi'],
        filters: [],
        expectedScopeGroups: [{ scopeType: 'standards', code: 'aichi' }]
      },
      {
        type: 'standards',
        data: ['gri-1'],
        filters: [{ type: 'standards', value: 'gri' }],
        expectedScopeGroups: [{ scopeType: 'standards', code: 'gri', scopeTags: ['gri-1'] }]
      },
      {
        type: 'standards',
        data: ['gri-1-102'],
        filters: [{ type: 'standards', value: 'gri' }, { type: 'group', value: 'gri-1' }],
        expectedScopeGroups: [{ scopeType: 'standards', code: 'gri', scopeTags: ['gri-1-102'] }]
      },
      {
        type: 'frameworks',
        data: ['cdsb-req01'],
        filters: [],
        expectedScopeGroups: [{ scopeType: 'frameworks', code: 'cdsb-req01' }]
      },
      {
        type: 'custom',
        data: ['5f0c5cc20c96b744ace99019'],
        filters: [],
        expectedScopeGroups: [{ scopeType: 'custom', code: '5f0c5cc20c96b744ace99019' }]
      },
      {
        type: 'materiality',
        data: [Materiality.High],
        filters: [],
        expectedScopeGroups: [
          { scopeType: 'sdg', code: '7' },
          { scopeType: 'sdg', code: '8' },
          { scopeType: 'sdg', code: '14' },
          { scopeType: 'sdg', code: '15' },
        ],
        expectedType: 'sdg',
      },
      {
        type: 'materiality',
        data: [Materiality.Low, Materiality.Medium],
        filters: [],
        expectedScopeGroups: [{ scopeType: 'sdg', code: '10' }, { scopeType: 'sdg', code: '9' }],
        expectedType: 'sdg'
      },
      {
        type: 'materiality',
        data: [Materiality.None],
        filters: [],
        expectedScopeGroups: ['1', '2', '3', '4', '5', '6', '11', '12', '13', '16', '17'].map(code => ({ scopeType: 'sdg', code })),
        expectedType: 'sdg'
      },
      {
        type: 'question-packs',
        data: ['ctl'],
        filters: [],
        expectedScopeGroups: [{ scopeType: 'frameworks', code: 'ctl' }]
      },
      {
        type: 'question-packs',
        data: ['aichi'],
        filters: [],
        expectedScopeGroups: [{ scopeType: 'standards', code: 'aichi' }]
      },
      {
        type: 'question-packs',
        data: ['5f0c5cc20c96b744ace99019'],
        filters: [],
        expectedScopeGroups: [{ scopeType: 'custom', code: '5f0c5cc20c96b744ace99019' }]
      },
      {
        type: 'question-packs',
        data: ['5f0c5cc20c96b744ace99019', 'ctl', 'aichi'],
        filters: [],
        expectedScopeGroups: [{ scopeType: 'custom', code: '5f0c5cc20c96b744ace99019' }, { scopeType: 'frameworks', code: 'ctl' }, { scopeType: 'standards', code: 'aichi' }]
      },
      {
        type: ViewValues.AssignedMetrics,
        data: ['5f0c5cc20c96b744ace99019'],
        filters: [],
        expectedScopeGroups: [{ scopeType: 'custom', code: '5f0c5cc20c96b744ace99019' }]
      },
      {
        type: ViewValues.AssignedMetrics,
        data: ['5f0c5cc20c96b744ace99019', '5f0c5cc20c96b744ace88888'],
        filters: [],
        expectedScopeGroups: [
          { scopeType: 'custom', code: '5f0c5cc20c96b744ace99019' },
          { scopeType: 'custom', code: '5f0c5cc20c96b744ace88888' },
        ]
      },
      {
        type: ViewValues.AllPacks,
        data: ['5f0c5cc20c96b744ace99019', 'ctl', 'aichi', '1'],
        filters: [],
        expectedScopeGroups: [
          { scopeType: 'custom', code: '5f0c5cc20c96b744ace99019' },
          { scopeType: 'frameworks', code: 'ctl' },
          { scopeType: 'standards', code: 'aichi' },
          { scopeType: 'sdg', code: '1' },
        ],
      },
      {
        type: ViewValues.AllPacks,
        data: [Materiality.High],
        filters: [],
        expectedScopeGroups: [
          { scopeType: 'sdg', code: '7' },
          { scopeType: 'sdg', code: '8' },
          { scopeType: 'sdg', code: '14' },
          { scopeType: 'sdg', code: '15' },
        ],
        expectedType: 'sdg',
      },
    ];

    dataProvider.forEach(({ type, data, filters, expectedScopeGroups }) => {
      test(`Data ${type} ${data}`, () => {
        const scopeGroups = processScopeData(type as ViewValues, data, filters, materiality);

        expect(scopeGroups).toEqual(expectedScopeGroups)

      })

    })

  })

});
