/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React from 'react';
import { ROUTES } from '../../constants/routes';
import { generateUrl } from '../../routes/util';
import { AccessType, MetricGroup } from '../../types/metricGroup';
import { CardProps } from '../../types/surveyScope';
import { ViewValues } from '@g17eco/types/surveyOverview';
import { CardGridButtonProps, CardGridGroup, CardGridItemProps } from './CardGrid';
import { getScopeInfo } from './scopeSelection';
import { PACK, QUESTION, SURVEY } from '@constants/terminology';
import { getVariant } from '@utils/metricGroup';


export const getCardIcon = (metricGroup: MetricGroup) => {
  if (metricGroup.groupData?.icon) {
    return <img alt={'icon'} src={metricGroup.groupData.icon} />
  }

  const metricGroupColour = metricGroup.groupData?.colour ?? '#e6e8ed';
  return <i className='survey-pack-icon' style={{ backgroundColor: metricGroupColour }} />;
};

export const getCustomMetricCards = ({ cardGroup, metricGroups, addBtn, handleDrilldown, breadcrumbs, surveyData, scopeType, materiality, handleCreateCustomMetric }: CardProps) => {
  const cardGroups: CardGridGroup[] = [];
  const isDrilldown = breadcrumbs && breadcrumbs.length > 0;
  const initiativeId = surveyData?.initiativeId;
  const surveyId = surveyData?._id;
  const query = surveyId ? `surveyId=${surveyId}` : '';

  const getButtons: (metricGroup: MetricGroup) => CardGridButtonProps[] = (metricGroup) => {
    const variant = getVariant(metricGroup);
    const buttons = [];
    buttons.push({
      icon: <i className='fal fa-cog' />,
      tooltip: variant.editTooltip,
      to: variant.editDisabled
        ? undefined
        : `${generateUrl(ROUTES.CUSTOM_METRICS, { initiativeId, groupId: metricGroup._id })}?${query}`,
    });

    if (metricGroup.groupData?.link) {
      buttons.push({
        icon: <i className='fa fa-link text-ThemeTextPlaceholder' />,
        tooltip: `${metricGroup.groupData?.link}`,
        onClick: () => window.open(metricGroup.groupData?.link),
      });
    }

    buttons.push({
      icon: <i className='fa fa-list text-ThemeTextPlaceholder' />,
      tooltip: `${metricGroup.groupName} ${QUESTION.CAPITALIZED_PLURAL} - Click to view`,
      onClick: () => handleDrilldown(ViewValues.QuestionList, String(metricGroup._id), String(metricGroup.groupName))
    });
    if (!isDrilldown) {
      buttons.push(addBtn(String(metricGroup._id), String(metricGroup.groupName)));
    }
    return buttons;
  }

  const getSectionName = (g: MetricGroup) => {
    if (cardGroup === ViewValues.Custom) {
      if (g.initiativeId === initiativeId) {
        return '';
      }
      if (g.accessType === AccessType.Inherited) {
        return 'Inherited';
      }
    }
    if (g.accessType === AccessType.Assigned) {
      return `Assigned${g.initiative?.name ? ` by ${g.initiative.name}` : ''}`;
    }
    return '';
  }

  const sectionGroups = metricGroups
    .filter(g => {
      if (cardGroup === ViewValues.Custom) {
        return g.accessType !== AccessType.Assigned; // Default + Inherited
      }
      if (cardGroup === ViewValues.AssignedMetrics) {
        return g.accessType === AccessType.Assigned;
      }
      return false;
    })
    .reduce((acc, g) => {
      const sectionName = getSectionName(g);
      if (!acc.has(sectionName)) {
        acc.set(sectionName, []);
      }
      acc.get(sectionName)?.push(g);
      return acc;
    }, new Map<string, MetricGroup[]>());

  sectionGroups.forEach((metricGroups, sectionName) => {
    const cardGridGroup: CardGridGroup = {
      name: sectionName ?? undefined,
      cards: metricGroups.map(
        (metricGroup: MetricGroup) => {
          const isInherited = metricGroup.accessType === AccessType.Inherited;
          const name = `${isInherited ? 'Inherited: ' : ''}${metricGroup.groupName}`;
          const scopeTag = String(metricGroup._id);
          const { inScope, isPartial } = getScopeInfo(surveyData.scope, scopeTag, scopeType, breadcrumbs, { materiality, metricGroups });
          const card: CardGridItemProps = {
            key: `scope-cardgriditem-metricgroup-${metricGroup._id}`,
            title: <><span>{name}</span></>,
            sortTitle: name,
            description: metricGroup.description ?? '',
            icon: getCardIcon(metricGroup),
            unitCount: metricGroup.universalTrackers?.length ?? 0,
            unitName: 'Metrics',
            buttons: getButtons(metricGroup),
            scopeTag,
            inScope,
            isPartial,
          }
          return card;
        })
    }
    cardGroups.push(cardGridGroup);
  });

  const createMetricCard: CardGridItemProps = {
    key: 'scope-cardgriditem-metricgroup-createnew',
    title: <span data-testid='create-metric-group-card'>Create a new {SURVEY.ADJECTIVE} {PACK.SINGULAR}</span>,
    sortTitle: `000000 Create a new ${SURVEY.ADJECTIVE} ${PACK.SINGULAR}`,
    subtitle: `Click here if you would like to create a new ${SURVEY.ADJECTIVE} ${PACK.SINGULAR} of custom metrics`,
    icon: <span className='new-survey-pack-icon' onClick={handleCreateCustomMetric}><i className='fa fa-plus' /></span>,
    unitCount: undefined,
    unitName: '',
    buttons: [
      {
        icon: <span className='px-3'>Create</span>,
        tooltip: `Create or Manage metric ${QUESTION.SINGULAR} groups`,
        to: `${generateUrl(ROUTES.CUSTOM_METRICS, { initiativeId, groupId: 'create' })}?surveyId=${surveyData._id}`,
      },
    ],
    scopeTag: '',
    inScope: false,
    isPartial: false,
  };

  if (initiativeId && cardGroup === ViewValues.Custom) {
    // Add create new pack button to the first group
    if (cardGroups.length === 0) {
      cardGroups.push({ cards: [createMetricCard] });
    } else {
      const firstGroup = cardGroups[0];
      cardGroups.splice(0, 1, {
        ...firstGroup,
        cards: [
          createMetricCard,
          ...firstGroup.cards,
        ],
      });
    }
  }
  return cardGroups.sort((a, b) => {
    // Empty undefined a goes to the front
    if (!a.name && b.name) {
      return -1;
    }

    if (a.name && !b.name) {
      return 1;
    }
    return 0;
  });
}
