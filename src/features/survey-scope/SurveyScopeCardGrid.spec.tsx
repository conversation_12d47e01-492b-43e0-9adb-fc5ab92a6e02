/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */
import { describe, it, expect, vi } from 'vitest';
import { screen } from '@testing-library/react';
import { setup } from '../../__fixtures__/utils';
import SurveyScopeCardGrid, { ScopeCardGridProps } from './SurveyScopeCardGrid';
import { ViewValues } from '@g17eco/types/surveyOverview';
import { SurveyActionData } from '../../types/surveyAction';
import * as cardGroups from './cardGroups';
import { CardGridGroup, CardGridItemProps, CardGridViewMode } from './CardGrid';
import { BrowserRouter } from 'react-router-dom';
import { reduxFixtureStore, createReduxLoadedState } from '../../__fixtures__/redux-store';
import { userOne } from '../../__fixtures__/user-factory';
import { RootState } from '../../reducers';
import { SurveyContextLoadedProps } from '@features/survey-container/SurveyContainer';

vi.mock('./scopeSelection', () => ({
  getScopeInfo: () => ({ inScope: false, isPartial: false }),
}));

const mockSurveyData: Pick<SurveyActionData, '_id' | 'scope' | 'scopeConfig' | 'initiativeId' | 'completedDate'> = {
  _id: 'survey-1',
  scope: { sdg: [], materiality: [], standards: [], frameworks: [], custom: [] },
  scopeConfig: [],
  initiativeId: 'initiative-1',
  completedDate: undefined,
};

const defaultProps: ScopeCardGridProps = {
  view: ViewValues.AllPacks,
  scopeType: ViewValues.AllPacks,
  cardGroup: ViewValues.AllPacks,
  surveyData: mockSurveyData,
  isLoading: false,
  updateScope: vi.fn(),
  isLoaded: true,
  breadcrumbs: [],
  setBreadcrumbs: vi.fn(),
  sidebarSettings: { viewLayout: CardGridViewMode.gallery } as SurveyContextLoadedProps['sidebarSettings'],
  questions: [],
  UNSDGMap: { goals: [] },
  materiality: undefined,
  blueprint: {},
  metricGroups: [],
  users: [],
  disabled: false,
  selectedView: ViewValues.AllPacks,
  setSelectedView: vi.fn(),
  scopeFilters: { filterByMateriality: [] },
  updateScopeFilters: vi.fn(),
  materialityMap: undefined,
  isReadOnly: false,
  reloadSurvey: vi.fn(),
  isUserManager: false,
};

const createMockCard = (props: Partial<CardGridItemProps>): CardGridItemProps => ({
  key: 'card-1',
  scopeTag: 'scope-tag-1',
  title: 'Card 1',
  sortTitle: 'Card 1',
  inScope: false,
  isPartial: false,
  ...props,
});

const defaultState = {
  currentUser: createReduxLoadedState(userOne),
  globalData: createReduxLoadedState({
    config: {
      features: [],
    },
    organization: { _id: 'org-1' },
  }),
} as unknown as RootState;

const renderComponent = (preloadedState: Partial<RootState> = {}) => {
  const store = reduxFixtureStore({
    ...defaultState,
    ...preloadedState,
  });

  return setup(
    <BrowserRouter>
      <SurveyScopeCardGrid {...defaultProps} />
    </BrowserRouter>,
    { store },
  );
};

describe('SurveyScopeCardGrid', () => {
  it('should render a card group as in scope', () => {
    const mockCard = createMockCard({ inScope: true, isPartial: false });
    const mockGroup: CardGridGroup = {
      name: 'Test Group',
      cards: [mockCard],
    };
    vi.spyOn(cardGroups, 'getCardGroups').mockReturnValue([mockGroup]);

    renderComponent();

    const card = screen.getByTestId('scope-tag-1');
    expect(card).toHaveClass('inScope');
    expect(card).not.toHaveClass('partialScope');
  });

  it('should render a card group as partial', () => {
    const mockCard = createMockCard({ inScope: false, isPartial: true });
    const mockGroup: CardGridGroup = {
      name: 'Test Group',
      cards: [mockCard],
    };
    vi.spyOn(cardGroups, 'getCardGroups').mockReturnValue([mockGroup]);

    renderComponent();

    const card = screen.getByTestId('scope-tag-1');
    expect(card).not.toHaveClass('inScope');
    expect(card).toHaveClass('partialScope');
  });
});
