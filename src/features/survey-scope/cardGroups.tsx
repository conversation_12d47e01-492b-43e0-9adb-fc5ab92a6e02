/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { CardProps } from '../../types/surveyScope';
import { CardGridGroup } from './CardGrid';
import {
  getAllCards,
  getFrameworksCards,
  getQuestionPackCards,
  getRatingsCards,
  getRegulatoryCards,
  getStandardsAndFrameworksCards,
  getStandardsCards,
} from './StandardsAndFrameworksCards';
import { getSDGCards, getSDGTargetCards } from './SdgCards';
import { getCustomMetricCards } from './CustomMetricCards';
import { getRolesCards } from './RolesCards';
import { getUsersCards } from './UsersCards';
import { ViewValues } from '@g17eco/types/surveyOverview';
import { getGroupCards, groupConfig, metricGroupConfig } from './GroupCards';

export const getCardGroups = (props: CardProps): CardGridGroup[] => {
  switch (props.cardGroup) {
    case ViewValues.Standards:
      return getStandardsCards(props);
    case ViewValues.Frameworks:
      return getFrameworksCards(props);
    case ViewValues.StandardsAndFrameworks:
      return getStandardsAndFrameworksCards(props);
    case ViewValues.Regulatory:
      return getRegulatoryCards(props);
    case ViewValues.Ratings:
      return getRatingsCards(props);
    case ViewValues.QuestionPacks:
      return getQuestionPackCards(props);
    case ViewValues.Group:
      return getGroupCards({ config: groupConfig, ...props });
    case ViewValues.CustomGroup:
      return getGroupCards({ config: metricGroupConfig, ...props });
    case ViewValues.SdgTarget:
      return getSDGTargetCards(props);
    case ViewValues.Sdg:
      return getSDGCards(props);
    case ViewValues.AllPacks:
      return getAllCards(props);
    case ViewValues.Archived:
      return getStandardsAndFrameworksCards({ ...props, isArchived: true });
    case ViewValues.Custom:
    default:
      return getCustomMetricCards(props);
  }
};

export const getDelegationCardGroups = (props: CardProps): CardGridGroup[] => {
  switch (props.cardGroup) {
    case ViewValues.Roles:
      return getRolesCards(props);
    case ViewValues.Users:
      return getUsersCards(props);
    default:
      return getCardGroups(props);
  }
};
