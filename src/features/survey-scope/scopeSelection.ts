/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { ViewValues } from '@g17eco/types/surveyOverview';
import { Breadcrumb, } from '@g17eco/types/surveyScope';
import { SurveyUserRoles } from '../../constants/user';
import { StakeholderGroup } from '../../model/stakeholderGroup';
import { frameworks, getGroup, Group, GroupTypes, standards, } from '@g17eco/core';
import { getMaterialityMap, MaterialityObject } from '@utils/survey/filters';
import { RequestScope } from '../../actions/api';
import { getDelegationScope, isVirtualType } from './virtualScope';
import { BreadcrumbFilter, convertToFilters } from '@utils/survey/useScopeFilters';
import { MaterialityMap } from '../../types/initiative';
import { isNumericString } from '../../utils/string';
import { MetricGroup } from '../../types/metricGroup';
import { getSubGroup, getMetricGroup, getGroupCode, getGroupChildrenTags } from '../../utils/groups';
import { SurveyModelMinData } from '@g17eco/types/survey';
import { ScopeGroups, SurveyRoles, SurveyScope } from '../../types/surveyCommon';
import { DelegationScopeOptions } from '../../types/surveyVirtualScope';

interface ScopeResult {
  inScope: boolean,
  isPartial: boolean
}

const isSubGroupInScope = <T extends Group | MetricGroup>(
  subGroup: T,
  subGroupCodes: string[]
): { inScope: boolean, isPartial: boolean } => {
  if (!subGroup.subgroups || subGroup.subgroups.length === 0) {
    return { inScope: false, isPartial: false };
  }

  const matchingCodes = subGroup.subgroups.filter((g) => {
    if (subGroupCodes.includes(getGroupCode(g))) {
      return true;
    }
    const { inScope, isPartial } = isSubGroupInScope(g as T, subGroupCodes);
    return inScope || isPartial;
  });

  // All codes match
  const inScope = matchingCodes.length === subGroup.subgroups.length;
  // Not all codes match, only some
  const isPartial = !inScope && matchingCodes.length > 0;

  return { inScope, isPartial }
}

const isDeepGroupInScope = <T extends Group | MetricGroup>(
  subGroups: T[],
  subGroupCodes: string[]
): { inScope: boolean, isPartial: boolean } => {
  let inScopeResult = false;
  for (const subGroup of subGroups) {
    const validCodes = getGroupChildrenTags(subGroup);

    if (!validCodes.some(c => subGroupCodes.includes(c))) {
      continue;
    }
    const { inScope, isPartial } = isSubGroupInScope(subGroup, subGroupCodes);
    inScopeResult = inScopeResult || inScope;

    if (isPartial) {
      return { inScope: inScope, isPartial: isPartial }
    } else if (subGroup.subgroups) {
      const scopeResult = isDeepGroupInScope(subGroup.subgroups as T[], subGroupCodes);
      if (scopeResult.isPartial) {
        return scopeResult
      }
      inScopeResult = inScopeResult || scopeResult.inScope;
    }
  }

  return { inScope: inScopeResult, isPartial: false }
}

interface GetScopeInfoOptions {
  materiality?: MaterialityObject;
  metricGroups?: MetricGroup[];
}

export const getScopeInfo = (
  scope: Partial<SurveyScope> | undefined,
  value: string,
  cardGroup: ViewValues | ScopeGroups,
  breadcrumbs: Breadcrumb[],
  options?: GetScopeInfoOptions
): ScopeResult => {
  const { materiality, metricGroups } = options || {};
  const materialityMap = getMaterialityMap(materiality, '');
  return getScopeInfoByMap(scope, value, cardGroup, breadcrumbs, { materialityMap, metricGroups });
};

interface GetScopeInfoByMapOptions {
  materialityMap?: MaterialityMap;
  metricGroups?: MetricGroup[];
}

export const getScopeInfoByMap = (
  scope: Partial<SurveyScope> | undefined,
  value: string,
  cardGroup: ViewValues | ScopeGroups,
  breadcrumbs: Breadcrumb[],
  options?: GetScopeInfoByMapOptions
): ScopeResult => {
  const { materialityMap, metricGroups } = options || {};
  const scopeType: ScopeGroups = Object.values(ScopeGroups).map(String).includes(cardGroup)
    ? (cardGroup as ScopeGroups)
    : ScopeGroups.Custom;

  const isSuperSet = isVirtualType(cardGroup);
  const scopeTypeData = isSuperSet
    ? [...(scope?.standards ?? []), ...(scope?.frameworks ?? []), ...(scope?.custom ?? []), ...(scope?.sdg ?? [])]
    : (scope?.[scopeType] ?? []);

  const inScope = scopeTypeData.includes(value);

  if (Object.keys(materialityMap ?? {}).includes(value)) {
    const sdgCode = materialityMap?.[value as keyof MaterialityMap] ?? [];
    const inScopeResult: boolean[] = [];

    for (const code of sdgCode) {
      const result = getScopeInfoByMap(scope, code, ViewValues.Sdg, breadcrumbs, { materialityMap, metricGroups });
      if (result.isPartial) {
        return result;
      }
      inScopeResult.push(result.inScope)
    }

    if (inScopeResult.length === 0) {
      return { inScope: false, isPartial: false };
    }

    const inScopeTrue = inScopeResult.filter(value => value === true)
    const inScope = inScopeTrue.length === inScopeResult.length

    return { inScope: inScope, isPartial: !inScope && inScopeTrue.length > 0 };
  }

  if (inScope) {
    return { inScope, isPartial: false };
  }

  const filters = convertToFilters(cardGroup as ViewValues, breadcrumbs ?? []);
  const [first, ...groupFilters] = filters;

  // if don't have group and it's virtual type, then try to get the type
  const viewValue = first?.value || value;
  const groupType = isSuperSet ? getScopeType(viewValue) : cardGroup;

  // Get the appropriate group based on scope type
  const group =
    metricGroups &&
    ((cardGroup === ViewValues.AllPacks && scopeType === ScopeGroups.Custom) ||
      [ViewValues.Custom, ViewValues.CustomGroup].includes(cardGroup as ViewValues))
      ? getMetricGroup(metricGroups, viewValue)
      : getGroup(groupType as GroupTypes, viewValue);

  if (!group) {
    return { inScope, isPartial: false };
  }

  // top level parent is included already
  const groupCode = getGroupCode(group);
  if (scopeTypeData.includes(groupCode)) {
    return { inScope: true, isPartial: false };
  }

  if (!first) {
    // Check if all child values are added
    return isDeepGroupInScope([group], scopeTypeData);
  }

  const groups: string[] = groupFilters
    .filter(t => t.value && [ViewValues.Group, ViewValues.CustomGroup, ViewValues.SdgTarget].includes(t.type as ViewValues))
    .map(t => t.value || '');

  let nextGroup = group;
  for (const subGroupCode of groups) {
    const subGroup = getSubGroup(group, subGroupCode);
    if (!subGroup) {
      return { inScope: false, isPartial: false };
    }

    if (getGroupCode(subGroup) === value) {
      return isSubGroupInScope(subGroup, scopeTypeData);
    }
    nextGroup = subGroup;
  }

  const deepGroup = getSubGroup(nextGroup, value);
  if (!deepGroup) {
    return { inScope: false, isPartial: false };
  }

  return isSubGroupInScope(deepGroup, scopeTypeData);
};

export const getScopeType = (value: string) => {
  if (standards[value]) {
    return 'standards'
  }
  if (frameworks[value]) {
    return 'frameworks'
  }
  if (isNumericString(value)) {
    return 'sdg'
  }
  return 'custom'
}

export const processScopeData = (
  cardGroup: ViewValues,
  data: string[],
  filters: BreadcrumbFilter[],
  materiality: MaterialityObject | undefined
): RequestScope[] => {
  const [firstB] = filters;

  const materialityMap = getMaterialityMap(materiality, '');
  if (data.every(code => Object.keys(materialityMap).includes(code))) {
    const scopeGroups: RequestScope[] = [];

    data.forEach(item => {
      if (Object.hasOwn(materialityMap, item)) {
        scopeGroups.push(...materialityMap[item as keyof MaterialityMap].map(code => ({ scopeType: ViewValues.Sdg, code })))
      }
    })
    return scopeGroups
  }

  const scopeGroups: RequestScope[] = [];

  if (isVirtualType(cardGroup) || cardGroup === ViewValues.AssignedMetrics) {
    if (firstB?.value) {
      scopeGroups.push({
        scopeType: getScopeType(firstB.value),
        code: firstB.value,
        scopeTags: data
      })
    } else {
      data.forEach((code) => scopeGroups.push({
        scopeType: getScopeType(code),
        code: code
      }));
    }
  } else {
    if (firstB?.value) {
      scopeGroups.push({
        scopeType: cardGroup,
        code: firstB.value,
        scopeTags: data
      })
    } else {
      data.forEach((code) => scopeGroups.push({
        scopeType: cardGroup,
        code: code
      }));
    }
  }
  return scopeGroups;
}

export const isInDelegationScope = (
  userId: string,
  surveyData: Partial<SurveyModelMinData>,
  delegationScope: DelegationScopeOptions,
  breadcrumbs: Breadcrumb[],
  userRole: keyof StakeholderGroup,
): boolean => {

  if (!userRole) {
    return false;
  }

  if ([SurveyUserRoles.Admin, SurveyUserRoles.Monitor].includes(userRole as SurveyUserRoles)) {
    return !!surveyData.roles?.[userRole as keyof SurveyRoles]?.includes(userId);
  }

  const userRoleViews = [ViewValues.Users, ViewValues.Roles];
  if (!delegationScope || userRoleViews.includes(delegationScope as ViewValues)) {
    return isSurveyDelegator(userId, surveyData, userRole);
  }

  const filters = convertToFilters(delegationScope as ViewValues, breadcrumbs);
  const [first, second] = filters.filter(b => !userRoleViews.includes(b.type as ViewValues))
  if (!first || !first.value) {
    return false;
  }
  const rootCardCategory = first.value;
  const delegationScopeKey = getDelegationScope(delegationScope, rootCardCategory);

  const delegationScopeElement = surveyData.delegationScope?.[delegationScopeKey];

  if (!delegationScopeElement || !rootCardCategory || !userRole) {
    return false;
  }

  if (second?.value) {
    // Deal with children
    const childDelegation = delegationScopeElement[rootCardCategory]?.children?.find(c => c.code === second.value);
    return !!childDelegation?.[userRole]?.includes(userId);
  }

  return !!delegationScopeElement[rootCardCategory]?.[userRole]?.includes(userId);
};

export const isSurveyDelegator = (
  userId: string,
  surveyData: Partial<SurveyModelMinData>,
  userRole: keyof StakeholderGroup,
): boolean => {
  return !!surveyData.stakeholders?.[userRole]?.includes(userId);
};
