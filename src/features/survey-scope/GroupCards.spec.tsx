/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */
import { ViewValues } from '@g17eco/types/surveyOverview';
import { describe, expect, it, vi } from 'vitest';
import { createUtr } from '../../__fixtures__/utr/utrv-factory';
import UniversalTracker from '../../model/UniversalTracker';
import { CardProps } from '../../types/surveyScope';
import { getGroupCards, groupConfig, metricGroupConfig } from './GroupCards';
import { MetricGroup } from '../../types/metricGroup';
import { createMetricGroup } from '../../__fixtures__/metric-group-fixtures';
import { faker } from '@faker-js/faker';

describe('getGroupCards', () => {
  const metricGroupId = faker.database.mongodbObjectId();
  const childGroupId1 = faker.database.mongodbObjectId();
  const childGroupId2 = faker.database.mongodbObjectId();
  const grandChildGroupId11 = faker.database.mongodbObjectId();
  const grandChildGroupId12 = faker.database.mongodbObjectId();

  const mockCardProps: CardProps = {
    view: ViewValues.AllPacks,
    surveyData: {
      _id: 'survey-1',
      scope: {
        sdg: [],
        materiality: [],
        standards: ['issb'],
        frameworks: [],
        custom: [grandChildGroupId11, childGroupId2],
      },
      scopeConfig: [],
      initiativeId: 'initiative-1',
    },
    cardGroup: ViewValues.AllPacks,
    questionList: [],
    UNSDGMap: { goals: [] },
    materiality: undefined,
    blueprint: { contributions: [], sdgContribution: '' },
    users: [],
    handleDrilldown: vi.fn(),
    breadcrumbs: [],
    metricGroups: [],
    addBtn: vi.fn(),
    scopeType: ViewValues.AllPacks,
    appSettings: {} as any,
    handleCreateCustomMetric: vi.fn(),
    selectedView: ViewValues.AllPacks,
    setSelectedView: vi.fn(),
    scopeFilters: { filterByMateriality: [] },
    updateScopeFilters: vi.fn(),
  };

  const issbUtrOne = createUtr('gri/2020/305-1/a', {
    alternatives: {
      issb: {
        name: 'Scope 1 greenhouse gas emissions',
        valueLabel: 'Scope 1 greenhouse gas emissions',
        typeTags: ['ifrs_s2', 'issb_metntar_s2', 'issb_metntar_s2_crm'],
      },
    },
  });

  const issbUtrTwo = createUtr('gri/2020/305-1/a', {
    type: 'issb',
    typeTags: ['ifrs_s1', 'issb_risk_s1'],
  });

  const questionList = [issbUtrOne, issbUtrTwo].map((utr) => ({
    universalTracker: new UniversalTracker(utr),
    name: utr.name,
    shortPrefix: '',
    frameworkCode: utr.typeCode,
    valueType: utr.valueType,
    valueValidation: {},
  }));

  describe('with groupConfig', () => {
    it('should return empty array if group has no subgroups', () => {
      const result = getGroupCards({
        ...mockCardProps,
        config: groupConfig,
        breadcrumbs: [{ cardGroup: ViewValues.Group, cardCategory: 'sgx-metrics', title: 'SGX Core' }],
      });
      expect(result).toEqual([]);
    });

    it('should return cards for subgroups', () => {
      const props = {
        ...mockCardProps,
        config: groupConfig,
        questionList,
        breadcrumbs: [
          {
            cardGroup: ViewValues.Group,
            cardCategory: 'issb',
            title: 'ISSB - International Sustainability Standards Board',
          },
        ],
      };
      const result = getGroupCards(props);
      expect(result[0].cards.length).toBe(2);
      const [ifrs_s1, ifrs_s2] = result[0].cards;

      expect(ifrs_s1.sortTitle).toEqual('IFRS - S1');
      expect(ifrs_s1.inScope).toBe(true);
      expect(ifrs_s2.sortTitle).toEqual('IFRS - S2');
      expect(ifrs_s2.inScope).toBe(true);
    });
  });

  describe('with metricGroupConfig', () => {
    const metricGroup = createMetricGroup({
      _id: metricGroupId,
      groupName: 'Metric Group',
      universalTrackers: [issbUtrOne._id, issbUtrTwo._id],
      subgroups: [
        {
          ...createMetricGroup({
            _id: childGroupId1,
            groupName: 'Child Group 1',
            subgroups: [
              createMetricGroup({
                _id: grandChildGroupId11,
                groupName: 'Grand Child Group 1.1',
                universalTrackers: [issbUtrOne._id],
              }),
              createMetricGroup({
                _id: grandChildGroupId12,
                groupName: 'Grand Child Group 1.2',
                universalTrackers: [issbUtrTwo._id],
              }),
            ],
            universalTrackers: [issbUtrOne._id, issbUtrTwo._id],
          }),
        },
        {
          ...createMetricGroup({
            _id: childGroupId2,
            groupName: 'Child Group 2',
            subgroups: [],
            universalTrackers: [issbUtrOne._id, issbUtrTwo._id],
          }),
        },
      ],
    });

    const metricGroupCardProps = {
      ...mockCardProps,
      config: metricGroupConfig,
      breadcrumbs: [
        {
          cardGroup: ViewValues.CustomGroup,
          cardCategory: metricGroupId,
          title: 'Metric Group',
        },
      ],
    };

    it('should return empty array if no group is found', () => {
      const result = getGroupCards(metricGroupCardProps);
      expect(result).toEqual([]);
    });

    it('should return empty array if group has no subgroups', () => {
      const props = {
        ...metricGroupCardProps,
        metricGroups: [{ ...metricGroup, subgroups: [] }],
      };
      const result = getGroupCards(props);
      expect(result[0].cards).toEqual([]);
    });

    it('should return cards for subgroups', () => {
      const props = {
        ...metricGroupCardProps,
        metricGroups: [metricGroup],
        questionList,
      };
      const result = getGroupCards(props);
      expect(result[0].cards.length).toBe(2);

      const [childGroup1, childGroup2] = result[0].cards;
      expect(childGroup1.sortTitle).toEqual('Child Group 1');
      expect(childGroup1.inScope).toBe(false);
      expect(childGroup1.isPartial).toBe(true);

      expect(childGroup2.sortTitle).toEqual('Child Group 2');
      expect(childGroup2.inScope).toBe(true);
      expect(childGroup2.isPartial).toBe(false);
    });
  });
});
