/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CardProps } from '../../types/surveyScope';
import { getSDGShortTitle, isHidden } from '../../constants/sdg-data';
import SDGIcon from '@g17eco/molecules/sdg-icon/sdg-icon';
import { ViewValues } from '@g17eco/types/surveyOverview';
import { CardGridButtonProps, CardGridGroup, CardGridItemProps } from './CardGrid';
import { filterSdg, MaterialityObject } from '@utils/survey/filters';
import { getScopeInfo } from './scopeSelection';
import { QUESTION } from '@constants/terminology';
import { BaseScopeQuestion } from '@g17eco/types/survey';

const getMaterialityButton = (materiality: MaterialityObject | undefined, sdgCode: string) => {
  const materialityPc = materiality?.[sdgCode] ?? 0;
  switch (true) {
    case materialityPc > 66:
      return {
        icon: <i className='fa fa-tachometer-alt text-ThemeDangerMedium' />,
        tooltip: 'High Materiality, Opportunity and Risk'
      };
    case materialityPc > 33:
      return {
        icon: <i className='fa fa-tachometer-alt text-ThemeWarningMedium' />,
        tooltip: 'Medium Materiality, Opportunity and Risk'
      };
    case materialityPc > 0:
      return {
        icon: <i className='fa fa-tachometer-alt text-ThemeSuccessMedium' />,
        tooltip: 'Low Materiality, Opportunity and Risk'
      };
    default:
      return {
        icon: <i className='fa fa-tachometer-alt' />,
        tooltip: 'No Materiality, Opportunity and Risk'
      };
  }
}

export const getSDGCards = ({ handleDrilldown, questionList, UNSDGMap, materiality, blueprint, addBtn, surveyData, scopeType, breadcrumbs }: CardProps) => {

  const cards: CardGridItemProps[] = [];
  UNSDGMap.goals.forEach(goal => {
    const filteredQuestionList: BaseScopeQuestion[] = [];
    questionList.forEach(question => {
      const utrCode = question.universalTracker.getCode();
      if (blueprint?.[utrCode]?.includes(`sdg/${goal.code}`)) {
        filteredQuestionList.push(question);
      }
    });
    if (filteredQuestionList.length === 0) {
      return;
    }
    const shortTitle = getSDGShortTitle(goal.code);
    const breadcrumbTitle = `Goal ${goal.code} ${shortTitle}`;
    const link = `https://sustainabledevelopment.un.org/sdg${goal.code}`;
    const scopeTag = String(goal.code);
    const { inScope, isPartial } = getScopeInfo(surveyData.scope, scopeTag, scopeType, breadcrumbs, materiality);
    cards.push({
      key: `scope-cardgriditem-sdg-${goal.code}`,
      title: <><span>Goal {goal.code}</span><span>{shortTitle}</span></>,
      sortTitle: `Goal ${goal.code} - ${shortTitle}`,
      subtitle: goal.title,
      description: goal.description,
      icon: <SDGIcon code={goal.code} width='40px' />,
      unitCount: filteredQuestionList.length,
      buttons: [
        getMaterialityButton(materiality, String(goal.code)),
        {
          icon: <i className='fa fa-link text-ThemeTextPlaceholder' />,
          tooltip: link,
          onClick: () => window.open(link)
        },
        {
          icon: <i className='fa fa-list text-ThemeTextPlaceholder' />,
          tooltip: `${breadcrumbTitle} ${QUESTION.CAPITALIZED_PLURAL} - Click to view`,
          onClick: () => handleDrilldown(ViewValues.SdgTarget, String(goal.code), breadcrumbTitle)
        },
        addBtn(String(goal.code), breadcrumbTitle),
      ],
      scopeTag,
      inScope,
      isPartial
    });
  });
  return [{
    cards
  }];
}

export const getSDGTargetCards = ({ handleDrilldown, questionList, materiality, UNSDGMap, blueprint, addBtn, breadcrumbs, surveyData, scopeType }: CardProps) => {

  const isDrilldown = breadcrumbs && breadcrumbs.length > 0;
  if (!isDrilldown) {
    return [];
  }

  const { cardCategory } = breadcrumbs[breadcrumbs.length - 1];

  const sdgCode = cardCategory;
  const goal = UNSDGMap.goals.find((goal) => goal.code === sdgCode);
  if (!goal) {
    return [];
  }

  const targets = goal.targets;

  const cardGroups: CardGridGroup[] = [];
  const cards: CardGridItemProps[] = [];

  targets.forEach((target: any) => {
    if (isHidden(target.code)) {
      return;
    }
    const filteredQuestionList = questionList.filter(q => filterSdg(q, blueprint, target.code));
    if (filteredQuestionList.length === 0) {
      return;
    }
    const shortTitle = getSDGShortTitle(target.code);
    const breadcrumbTitle = `Target ${target.code} ${shortTitle}`;
    const link = `https://sustainabledevelopment.un.org/sdg${goal.code}`;
    const buttons: CardGridButtonProps[] = [
      getMaterialityButton(materiality, goal.code),
      {
        icon: <i className='fa fa-link text-ThemeTextPlaceholder' />,
        tooltip: link,
        onClick: () => window.open(link)
      },
      {
        icon: <i className='fa fa-list text-ThemeTextPlaceholder' />,
        tooltip: `${breadcrumbTitle} ${QUESTION.CAPITALIZED_PLURAL} - Click to view`,
        onClick: () => handleDrilldown(ViewValues.StandardsQuestionList, target.code, breadcrumbTitle)
      },
      addBtn(String(target.code), breadcrumbTitle),
    ];
    if (!isDrilldown) {
      buttons.push(addBtn(String(target.code), breadcrumbTitle));
    }
    const scopeTag = String(target.code);
    const { inScope, isPartial } = getScopeInfo(surveyData.scope, scopeTag, scopeType, breadcrumbs, { materiality });
    cards.push({
      key: `scope-cardgriditem-sdg-${target.code}`,
      title: <><span>Target {target.code}</span><span>{shortTitle}</span></>,
      sortTitle: `Target ${target.code} - ${shortTitle}`,
      subtitle: target.title,
      description: target.description,
      icon: <SDGIcon code={target.code} width='40px' />,
      unitCount: filteredQuestionList.length,
      buttons: buttons,
      scopeTag,
      inScope,
      isPartial
    });
  });

  cardGroups.push({
    cards
  });
  return cardGroups;
}
