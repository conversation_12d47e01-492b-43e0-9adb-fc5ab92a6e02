import { ROUTES } from '@constants/routes';
import { QUESTION } from '@constants/terminology';
import { InheritedIcon } from '@features/survey/scope-group/InheritedIcon';
import { AccessType, MetricGroup } from '@g17eco/types/metricGroup';
import { ViewValues } from '@g17eco/types/surveyOverview';
import { CardProps } from '@g17eco/types/surveyScope';
import { generateUrl } from '@routes/util';
import { getVariant } from '@utils/metricGroup';
import { CardGridButtonProps, CardGridGroup, CardGridItemProps } from '../CardGrid';
import { getCardIcon } from '../CustomMetricCards';
import { getScopeInfo } from '../scopeSelection';

export const getRequiredCustomMetrics = (props: CardProps) => {
  const { surveyData, breadcrumbs, cardGroup, metricGroups, addBtn, handleDrilldown, scopeType, materiality } = props;

  const cardGroups: CardGridGroup[] = [];
  const isDrilldown = breadcrumbs && breadcrumbs.length > 0;
  const initiativeId = surveyData?.initiativeId;
  const surveyId = surveyData?._id;

  const getButtons: (metricGroup: MetricGroup) => CardGridButtonProps[] = (metricGroup) => {
    const buttons: CardGridButtonProps[] = [];
    const variant = getVariant(metricGroup);
    const inScope = surveyData.scope?.custom.includes(metricGroup._id);

    if (metricGroup.accessType !== AccessType.Assigned) {
      buttons.push({
        icon: <i className='fal fa-cog' />,
        tooltip: variant.editTooltip,
        to: variant.editDisabled
          ? undefined
          : `${generateUrl(ROUTES.CUSTOM_METRICS, { initiativeId, groupId: metricGroup._id })}?surveyId=${surveyId}`,
      });
    }

    if (metricGroup.groupData?.link) {
      buttons.push({
        icon: <i className='fa fa-link' />,
        tooltip: `${metricGroup.groupData?.link}`,
        onClick: () => window.open(metricGroup.groupData?.link),
      });
    }

    const hasSubGroups = metricGroup.subgroups && metricGroup.subgroups.length > 0;

    if (hasSubGroups) {
      buttons.push({
        icon: <i className='fa fa-list' />,
        tooltip: `${metricGroup.groupName} Groups - Click to view`,
        onClick: () => handleDrilldown(ViewValues.CustomGroup, String(metricGroup._id), String(metricGroup.groupName)),
      });
    } else {
      buttons.push({
        icon: <i className='fa fa-list' />,
        tooltip: `${metricGroup.groupName} ${QUESTION.CAPITALIZED_PLURAL} - Click to view`,
        onClick: () => handleDrilldown(ViewValues.QuestionList, String(metricGroup._id), String(metricGroup.groupName)),
      });
    }

    const hasRequiredScope =
      surveyData.scopeConfig &&
      surveyData.scopeConfig?.some((scope) => scope.code === metricGroup._id && scope.required);
    if (inScope && hasRequiredScope) {
      return buttons;
    }

    if (!isDrilldown) {
      buttons.push(addBtn(String(metricGroup._id), String(metricGroup.groupName)));
    }
    return buttons;
  };

  const inheritedCards: CardGridItemProps[] = [];
  const otherCards: CardGridItemProps[] = [];
  metricGroups.forEach((metricGroup: MetricGroup) => {
    const isInherited = metricGroup.accessType === AccessType.Inherited;
    const isAssigned = metricGroup.accessType === AccessType.Assigned;
    const name = `${isAssigned ? `${metricGroup.initiative?.name} assigned: ` : ''}${metricGroup.groupName}`;
    const scopeTag = String(metricGroup._id);
    const { inScope, isPartial } = getScopeInfo(surveyData.scope, scopeTag, scopeType, breadcrumbs, {
      materiality,
      metricGroups,
    });

    const card: CardGridItemProps = {
      key: `scope-cardgriditem-metricgroup-${metricGroup._id}`,
      title: <span>{name}</span>,
      sortTitle: name,
      description: metricGroup.description ?? '',
      icon: getCardIcon(metricGroup),
      inheritedIcon: isInherited ? <InheritedIcon /> : null,
      unitCount: metricGroup.universalTrackers?.length ?? 0,
      unitName: 'Metrics',
      buttons: getButtons(metricGroup),
      scopeTag,
      inScope,
      isPartial,
      initiativeId: metricGroup.initiativeId,
    };
    if (isInherited) {
      inheritedCards.push(card);
    } else {
      otherCards.push(card);
    }
  });
  if (surveyId && initiativeId && cardGroup === ViewValues.Custom) {
    otherCards.push({
      key: 'scope-cardgriditem-metricgroup-createnew',
      title: <span>Create a new metric group</span>,
      sortTitle: '000000 Create a new metric group',
      subtitle: 'Click here if you would like to create a new group of custom metrics',
      icon: <i className={'fal fa-file-circle-question'} />,
      unitCount: undefined,
      unitName: '',
      buttons: [
        {
          icon: <span className='px-3'>Create</span>,
          tooltip: `Create or Manage metric ${QUESTION.SINGULAR} groups`,
          to: `${generateUrl(ROUTES.CUSTOM_METRICS, { initiativeId, groupId: 'create' })}?surveyId=${surveyId}`,
        },
      ],
      scopeTag: '',
      inScope: false,
      isPartial: false,
    });
  }
  cardGroups.push({
    cards: inheritedCards,
  });
  cardGroups.push({
    cards: otherCards,
  });

  return cardGroups;
};
