/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React, { Fragment, useEffect, useState } from 'react';
import { SurveyContextLoadedProps } from '@features/survey-container/SurveyContainer';
import CardGrid, { CardGridGroup, CardGridItem, CardGridItemProps } from './CardGrid';
import { Button } from 'reactstrap';
import SurveySettingsToolbar from '@features/survey-settings-toolbar';
import { CardProps, handleDrilldownInterface } from '../../types/surveyScope';
import { ViewValues } from '@g17eco/types/surveyOverview';
import { SurveyModelMinData } from '../../types/survey';
import { SurveyActionData } from '../../types/surveyAction';
import { ScopeQuestionList, shouldShowScopeQuestions } from '../survey-scope-question-list/ScopeQuestionList';
import { getScopeInfo } from './scopeSelection';
import { getCardGroups } from './cardGroups';
import { Action } from '../../constants/action';
import { useAppDispatch, useAppSelector } from '../../reducers';
import SurveyGroupHeader from './SurveyGroupHeader';
import classNames from 'classnames';
import { Sort } from '../../utils/sort';
import { addSiteAlert, SiteAlertColors } from '../../slice/siteAlertsSlice';
import { isOrgManager } from '../../selectors/user';
import G17Client from '../../services/G17Client';
import { useHistory, useLocation } from 'react-router-dom';
import { canAccessCustomScopeGroup, hasRequiredTags } from '../../constants/groups';
import './style.scss';
import { useSearchHistory } from './hooks/useSearchHistory';
import { useAppSettings } from '../../hooks/app/useAppSettings';
import { ROUTES } from '../../constants/routes';
import { generateUrl } from '../../routes/util';
import { RemoveArchivedScopeModal } from './RemoveArchivedScopeModal';
import { CardGroupName, sortByPurchased } from './utils';
import { CardGridFooter } from './CardGridFooter';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { useSearchParams } from '../../hooks/useSearchParams';
import { QUESTION } from '@constants/terminology';
import { UpgradeScopePack } from './scope-packs/UpgradeScopePack';
import { useScopePacksFeature } from './scope-packs/useScopePacksFeature';
import { SearchDebounce } from '@features/survey/survey-question-list-toolbar/partials/SearchQuestions';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';

const NUM_OF_CUSTOM_CARDS = 6;
const NUM_OF_FREE_CARDS = 9;

export interface ScopeCardGridProps extends Omit<SurveyContextLoadedProps, 'surveyData' | 'utrvId' | 'removeUtrvIdFromSearchParams' | 'handleChangeSettings'> {
  view: ViewValues;
  scopeType: ViewValues;
  cardGroup: ViewValues;
  showAddAllButtons?: boolean;
  surveyData: Pick<SurveyActionData, '_id' | 'scope' | 'scopeConfig' | 'initiativeId' | 'completedDate'>;
  isLoading: boolean;
  updateScope: (data: string[], method: Action) => void;
}

export default function SurveyScopeCardGrid(props: ScopeCardGridProps) {
  const {
    cardGroup,
    scopeType,
    view,
    showAddAllButtons = true,
    isLoaded,
    surveyData,
    breadcrumbs,
    setBreadcrumbs,
    sidebarSettings,
    questions,
    UNSDGMap,
    materiality,
    blueprint,
    metricGroups,
    users,
    disabled,
    isLoading,
    updateScope,
    selectedView,
    setSelectedView,
    scopeFilters,
    updateScopeFilters,
    materialityMap
  } = props;

  const { viewLayout } = sidebarSettings;
  const isDisabled = Boolean(surveyData.completedDate) || disabled;

  const dispatch = useAppDispatch();

  const [currentSort, setSort] = useState(Sort.TitleAsc);

  const searchParam = useSearchParams().get('search') || '';
  const [filterText, setFilterText] = useState(searchParam);
  // Since we use uncontrolled component SearchDebounce for performance, need additional state for managing default value.
  const [defaultSearchText, setDefaultSearchText] = useState(searchParam)

  const [{ showAllFreeCards, showAllCustomCards }, setShowCards] = useState({ showAllFreeCards: !!searchParam, showAllCustomCards: !!searchParam })
  const [removeScope, setRemoveScope] = useState<string>();
  const location = useLocation();
  const globalData = useAppSelector(state => state.globalData);
  const showAsQuestionList = cardGroup === ViewValues.QuestionList;
  const isDrilldown = breadcrumbs && breadcrumbs.length > 0;

  const globalScopeConfig = globalData.loaded ? globalData.data.config?.survey?.scope : undefined;
  const rootInitiative = globalData.loaded ? globalData.data.organization : undefined;
  const isManager = useAppSelector(isOrgManager);

  const { isRestricted, onUpgrade } = useScopePacksFeature({ initiativeId: surveyData.initiativeId });

  const { addSearchHistory, getSearchHistory, removeHistory } = useSearchHistory();
  const history = useHistory();

  const appSettings = useAppSettings();
  const { canViewAllPacks } = appSettings;

  useEffect(() => {
    if (searchParam) {
      // If redirect from insight page which includes searchParam. Don't override filterText using SearchHistory.
      return;
    }

    const searchTerm = getSearchHistory(location.pathname);
    setDefaultSearchText(searchTerm);
    setFilterText(searchTerm)
    removeHistory(location.pathname);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.pathname]);

  const addBtn: CardProps['addBtn'] = (scopeTag, cardName, group) => {
    const { inScope, isPartial } = getScopeInfo(surveyData.scope, scopeTag, scopeType, breadcrumbs, { materiality, metricGroups });

    // Should always use globalScopeConfig?
    const scopeConfig = surveyData.scopeConfig ?? globalScopeConfig;
    const isPartialScope = inScope || isPartial;
    const isPremium = hasRequiredTags(group?.requiredTags);
    const canAccess = !isPremium ||
      (group && canAccessCustomScopeGroup(group.requiredTags, scopeConfig)) ||
      canAccessCustomScopeGroup([scopeTag], scopeConfig)

    if (!canAccess && !isPartialScope) {
      const productCodes = group?.requiredTags ?? [];
      const canUpgrade = rootInitiative && isManager && productCodes.length > 0;
      const premiumClick = !canUpgrade ? undefined :
        () => G17Client.createCheckoutSession({
          initiativeId: rootInitiative._id,
          productCodes,
          returnUrl: location.pathname,
        }).then(s => window.location.href = s.url)
          .catch(e => dispatch(addSiteAlert({
            content: e.message,
            color: SiteAlertColors.Danger,
          })));

      return {
        tooltip: cardName ? `${isPartialScope ? 'Remove' : 'Add'} ${cardName} to the Report's Scope` : undefined,
        button: <Button
          outline={!isPartialScope}
          disabled={!canUpgrade || isDisabled}
          color={isPartialScope ? 'danger' : 'secondary'}
          className={classNames({
            'icon-button': true,
            'card-grid-button': true,
          })} onClick={premiumClick}>
          <span className='px-3'>Buy</span>
        </Button>
      }
    }

    // At this point either it has access or it's already in scope and can be removed
    const onClick = () => {
      if (group?.hidden && isPartialScope) {
        setRemoveScope(scopeTag)
      } else {
        updateScope([scopeTag], isPartialScope ? Action.Remove : Action.Add);
      }
    }
    return {
      tooltip: cardName ? `${isPartialScope ? 'Remove' : 'Add'} ${cardName} to the Report's Scope` : undefined,
      button: <Button
        outline={!isPartialScope}
        onClick={onClick}
        disabled={isDisabled}
        color={isPartialScope ? 'danger' : 'primary'}
        className={classNames({
          'icon-button': true,
          'card-grid-button': true,
        })}>
        <i className={`fa ${isPartialScope ? 'fa-times' : 'fa-plus'}`} />
      </Button>
    }
  }

  const handleDrilldown: handleDrilldownInterface = (cardGroup, cardCategory, title, groupBy) => {
    // When drill back, we use /all-packs so when drill down, need to save history using /all-packs too.
    addSearchHistory(filterText, location.pathname.endsWith('scope') ? `${location.pathname}/all-packs` : location.pathname);
    setFilterText('');
    setDefaultSearchText('');
    // At this point, don't need searchParam anymore since we use SearchHistory.
    history.location.search = '';

    if (groupBy) {
      setBreadcrumbs([{ cardGroup, cardCategory, title }], groupBy);
    } else {
      setBreadcrumbs([...breadcrumbs, { cardGroup, cardCategory, title }]);
    }
  }

  const handleCreateCustomMetric = () => {
    const url = `${generateUrl(ROUTES.CUSTOM_METRICS, { initiativeId: surveyData.initiativeId, groupId: 'create' })}?surveyId=${surveyData._id}`;
    history.push(url);
  }

  const cardGroupProps: CardProps = {
    view,
    surveyData,
    cardGroup,
    questionList: questions,
    UNSDGMap,
    materiality,
    blueprint,
    users,
    handleDrilldown,
    breadcrumbs,
    metricGroups,
    addBtn,
    scopeType,
    appSettings,
    handleCreateCustomMetric,
    selectedView,
    setSelectedView,
    scopeFilters,
    updateScopeFilters
  };

  const gridGroups = !isLoaded ? [] : getCardGroups(cardGroupProps);
  const archivedGroups = !isLoaded ? [] : getCardGroups({...cardGroupProps, cardGroup: ViewValues.Archived });

  const cardGroups = isRestricted ? gridGroups.reduce<CardGridGroup[]>((acc, group) => {
    if (![CardGroupName.Free, CardGroupName.Premium].includes(group.name as CardGroupName)) {
      acc.push(group);
      return acc;
    }
    // keep free or premium that's in scope
    const cards = group.cards.filter((card) => card.inScope);
    // only render the switcher if there are archived cards
    const hasArchived = archivedGroups.find((g) => g.name === group.name)?.cards.some((card) => card.inScope) ?? false;
    if (cards.length || hasArchived) {
      acc.push({ ...group, cards, component: hasArchived ? group.component : undefined });
    }
    return acc;
  }, []) : gridGroups;

  const showStandardQuestionList = shouldShowScopeQuestions(cardGroup);

  const handleSearch = (text: string) => {
    const isShowCards = !!text;
    setShowCards({ showAllFreeCards: isShowCards, showAllCustomCards: isShowCards });
    return setFilterText(text)
  }

  const isStandardsAndFrameworks = selectedView === ViewValues.StandardsAndFrameworks;
  const isSDG = selectedView === ViewValues.Sdg;

  const isMatched = (sortTitle: string, searchTerm: string) => {
    const title = sortTitle.toLowerCase().replace(' -', '');
    return searchTerm
      .toLowerCase()
      .split(',') // support multi search term to filter multiple scopes. ex: 'tcfd,see,sec'
      .some((term) => term && title.includes(term));
  };

  const filterByMateriality = scopeFilters.filterByMateriality;
  const excludedMaterialityScopeTags =
    filterByMateriality.length > 0 && materialityMap
      ? Object.entries(materialityMap).reduce((acc, [key, scopeTags]) => {
          if (!filterByMateriality.includes(key)) {
            acc.push(...scopeTags);
          }
          return acc;
        }, [] as string[])
      : [];

  const cardFilter = ({
    card,
    cardIndex,
    groupName,
  }: {
    card: CardGridItemProps;
    cardIndex: number;
    groupName?: string;
  }) => {
    if (filterText && !isMatched(card.sortTitle, filterText)) {
      return false;
    }

    if (isSDG && excludedMaterialityScopeTags.includes(card.scopeTag)) {
      return false;
    }

    if (isDrilldown || groupName === CardGroupName.Premium || (!canViewAllPacks && groupName === CardGroupName.Free)) {
      return true;
    }

    if (groupName === CardGroupName.Free) {
      return !isStandardsAndFrameworks || showAllFreeCards || cardIndex < NUM_OF_FREE_CARDS;
    }

    // Custom cards group
    return showAllCustomCards || cardIndex < NUM_OF_CUSTOM_CARDS;
  };

  const removeArchivedScope = () => {
    if (!removeScope) return;
    updateScope([removeScope], Action.Remove);
    setRemoveScope('');
  }

  const sortCards = (group: CardGridGroup) => {
    if (group.name === CardGroupName.Premium) {
      return sortByPurchased(group.cards, surveyData.scopeConfig);
    }
    return group.cards;
  }

  const renderCardGridFooter = (group: CardGridGroup) => {
    const isFreeSection =
      showAddAllButtons && !isDrilldown && canViewAllPacks && group.name === CardGroupName.Free;
    const isCustomSection =
      showAddAllButtons &&
      !isDrilldown &&
      group.name !== CardGroupName.Free &&
      group.name !== CardGroupName.Premium;

    // Buttons group shows only for Free and Custom section
    if (group.cards.length === 0 || (!isFreeSection && !isCustomSection)) {
      return null;
    }

    if (isFreeSection) {
      return (
        <CardGridFooter
          cardGroups={[group]}
          surveyData={surveyData}
          updateScope={updateScope}
          disabledAddRemoveScope={isDisabled}
          showMoreLess={isStandardsAndFrameworks}
          disableMoreLess={Boolean(filterText)} // When searching, searchs on all free cards
          showAllCards={showAllFreeCards}
          toggleAllCards={() => setShowCards((prev) => ({ ...prev, showAllFreeCards: !prev.showAllFreeCards }))}
        />
      );
    }

    return (
      <CardGridFooter
        cardGroups={[]}
        surveyData={surveyData}
        updateScope={updateScope}
        disabledAddRemoveScope={isDisabled}
        showMoreLess={group.cards.length > NUM_OF_CUSTOM_CARDS}
        disableMoreLess={Boolean(filterText)} // When searching, searchs on all custom cards
        showAllCards={showAllCustomCards}
        toggleAllCards={() => setShowCards((prev) => ({ ...prev, showAllCustomCards: !prev.showAllCustomCards }))}
      />
    );
  };

  const renderCardGroups = () => {
    return (
      <>
        {cardGroups.map((group, i) => {
          const cardList = sortCards(group).filter((card, cardIndex) =>
            cardFilter({ card, cardIndex, groupName: group.name })
          );
          return (
            <Fragment key={`survey-scope-card-group-${i}`}>
              <SurveyGroupHeader group={group} />
              <CardGrid viewLayout={viewLayout} emptyMessage='Not found'>
                {cardList.map((card: CardGridItemProps, i) => {
                  const itemProps = {
                    unitName: QUESTION.CAPITALIZED_PLURAL,
                    ...card,
                    className: classNames(
                      card.className,
                      card.inScope ? 'inScope' : card.isPartial ? 'partialScope' : 'notInScope'
                    ),
                    disabled: isLoading,
                  };
                  return <CardGridItem {...itemProps} key={card.key} />;
                })}
              </CardGrid>
              {renderCardGridFooter(group)}
            </Fragment>
          );
        })}
      </>
    );
  };

  return (
    <>
      {isLoaded && isLoading ? <BlockingLoader /> : <></>}
      <div className='mt-3'>
        <LoadingPlaceholder count={1} height={39} isLoading={!isLoaded}>
          <SurveySettingsToolbar
            view={view}
            breadcrumbs={breadcrumbs}
            handleDrillback={setBreadcrumbs}
            currentSort={currentSort}
            handleSort={setSort}
          >
            <div className='mr-2'>
              <SearchDebounce
                key={location.pathname + defaultSearchText} // Force rerender to update state.
                placeholder='Search'
                handleChange={handleSearch}
                defaultValue={defaultSearchText}
                delay={200}
              />
            </div>
          </SurveySettingsToolbar>
        </LoadingPlaceholder>
      </div>
      <LoadingPlaceholder className='mt-3' count={1} height={115} isLoading={!isLoaded}>
        <div className='mt-3'>
          {showAsQuestionList || showStandardQuestionList ? (
            <ScopeQuestionList
              breadcrumbs={breadcrumbs}
              surveyData={surveyData as SurveyModelMinData}
              questions={questions}
              view={view}
            />
          ) : (
            renderCardGroups()
          )}
        </div>
        <UpgradeScopePack isRestricted={isRestricted} onUpgrade={onUpgrade} />
        {removeScope ? (
          <RemoveArchivedScopeModal handleSubmit={removeArchivedScope} handleClose={() => setRemoveScope('')} />
        ) : null}
      </LoadingPlaceholder>
    </>
  );
}
