import { CalculatorInfo } from '@g17eco/types/carbon-calculator';
import { DashboardSection } from '@g17eco/molecules/dashboard';
import { Badge, Button } from 'reactstrap';

const CalculatorBadges = ({ badges }: { badges: string[] }) => (
  <>
    {badges.map((badge) => (
      <Badge
        key={`badge-${badge}`}
        pill
        className='me-2 bg-transparent text-ThemeSuccessMedium fw-normal border border-ThemeSuccessMedium'
        color='success'
      >
        {badge}
      </Badge>
    ))}
  </>
);

export const EmissionsCalculatorList = ({
  calculators,
  handleGoToLink,
  handleSetLightBox,
  getButtons,
}: {
  calculators: CalculatorInfo[];
  handleGoToLink: (params: { link: string; code: string }) => void;
  handleSetLightBox: (params: { images: string[]; index: number }) => void;
  getButtons?: (calculator: CalculatorInfo) => React.JSX.Element[];
}) => {
  return (
    <>
      {calculators.map((calculator, i) => (
        <DashboardSection key={`calculator-${calculator.name}-${i}`} padding={2}>
          <div className='d-flex flex-column'>
            <div className='d-flex'>
              <div>
                <img className='calculators-logo' src={calculator.logo} width={60} height={60} alt={calculator.name} />
              </div>
              <div className='d-flex flex-column flex-fill'>
                <div>
                  <h3 className='text-ThemeTextDark'>{calculator.name}</h3>
                </div>
                <div>
                  <CalculatorBadges badges={calculator.tags} />
                </div>
              </div>
              <div>
                {calculator.link ? (
                  <Button
                    color='transparent'
                    outline
                    onClick={() =>
                      handleGoToLink({
                        link: calculator.link as string,
                        code: calculator.code,
                      })
                    }
                  >
                    Website
                    <i className='ms-2 fal fa-arrow-up-right' />
                  </Button>
                ) : null}
                {getButtons?.(calculator)}
              </div>
            </div>
          </div>
          <hr />
          <>{calculator.description}</>
          {calculator.images ? (
            <div className='mt-3'>
              {calculator.images.map((image, i) => (
                <Button
                  key={`img-${calculator.name}-${image}`}
                  color='link-secondary'
                  onClick={() => handleSetLightBox({ images: calculator.images ?? [], index: i })}
                >
                  <img alt={`${calculator.name} ${i + 1}`} className='' src={image} height={60} width={60} />
                </Button>
              ))}
            </div>
          ) : null}
          <hr />

          <table className='highlights-and-restrictions' width='100%'>
            <tbody>
              <tr>
                <td width='49%'>
                  {calculator.highlights.length > 0 ? (
                    <>
                      <div className='mb-1'>Great for:</div>
                      <ul className='list-highlights'>
                        {calculator.highlights.map((bullet) => (
                          <li key={`li1-${bullet}`}>{bullet}</li>
                        ))}
                      </ul>
                    </>
                  ) : null}
                </td>
                <td width='2%' />
                <td width='49%'>
                  {calculator.restrictions.length > 0 ? (
                    <>
                      <div className='mb-1'>Worth checking:</div>
                      <ul className='list-restrictions'>
                        {calculator.restrictions.map((bullet) => (
                          <li key={`li2-${bullet}`}>{bullet}</li>
                        ))}
                      </ul>
                    </>
                  ) : null}
                </td>
              </tr>
            </tbody>
          </table>
        </DashboardSection>
      ))}
    </>
  );
};
