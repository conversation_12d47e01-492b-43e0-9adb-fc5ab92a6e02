import { Group } from '@g17eco/core';
import { MetricGroup } from '@g17eco/types/metricGroup';

function isMetricGroup(group: Group | MetricGroup): group is MetricGroup {
  return '_id' in group && 'groupName' in group;
}

export function getGroupCode(group: Group | MetricGroup): string {
  return isMetricGroup(group) ? group._id : group.code;
}

export const getSubGroup = (group: Group | MetricGroup, code: string): Group | MetricGroup | undefined => {
  return group.subgroups?.find((g) => getGroupCode(g) === code);
};

export function findChildGroupByCode<T extends Group | MetricGroup>(group: T, code: string): T | undefined {
  if (!group.subgroups) {
    return undefined;
  }

  for (const groupElement of group.subgroups) {
    if (getGroupCode(groupElement) === code) {
      return groupElement as T;
    }

    const childGroup = findChildGroupByCode(groupElement as T, code);
    if (childGroup) {
      return childGroup;
    }
  }

  return undefined;
}

export const flattenBySubgroups = (data: MetricGroup[]) => {
  const result = [];
  const nodesToProcess = [...data];

  while (nodesToProcess.length > 0) {
    const currentNode = nodesToProcess.shift();
    if (!currentNode) {
      continue;
    }
    const { subgroups, ...rest } = currentNode;
    result.push(rest);
    if (Array.isArray(subgroups) && subgroups.length > 0) {
      nodesToProcess.push(...subgroups);
    }
  }

  return result;
};

export const getMetricGroup = (metricGroups: MetricGroup[], groupId: string) => {
  return metricGroups.find((g) => g._id === groupId);
};

export const getMetricSubgroup = (group: MetricGroup, groupId: string): MetricGroup | undefined => {
  return findChildGroupByCode(group, groupId);
};

export function getGroupChildrenTags<T extends Group | MetricGroup>(group: T): string[] {
  if (!group.subgroups || group.subgroups.length === 0) {
    return [];
  }

  const tags: string[] = [];
  const queue = [...group.subgroups];

  while (queue.length > 0) {
    const current = queue.shift();
    if (current) {
      tags.push(getGroupCode(current));
      if (current.subgroups && current.subgroups.length > 0) {
        queue.push(...current.subgroups);
      }
    }
  }

  return tags;
}
