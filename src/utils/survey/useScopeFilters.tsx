/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { useMemo } from 'react';
import { Materiality, MaterialityMap } from '../../types/initiative';
import { MetricGroup } from '../../types/metricGroup';
import { BlueprintContributions, ScopeQuestion, ScopeQuestionOptionalValue } from '@g17eco/types/survey';
import { Breadcrumb } from '../../types/surveyScope';
import { ViewValues } from '@g17eco/types/surveyOverview';
import {
  filterFramework,
  filterMateriality,
  filterSdg,
  filterStandard,
  filterStandardAndFramework,
  getMaterialityMap,
  MaterialityObject,
  filterUsers,
  filterQuestionsByRoles,
  filterQuestionPacks
} from './filters';
import { filterMetricGroups } from '@utils/metricGroup';
import { flattenBySubgroups } from '../groups';

const filterByType = ({
  question,
  filterType,
  filterValue,
  materiality,
  blueprint,
  metricGroups,
}: {
  question: ScopeQuestionOptionalValue;
  filterType: ViewValues | string;
  filterValue: string;
  materiality: MaterialityMap;
  blueprint: BlueprintContributions;
  metricGroups?: MetricGroup[];
}) => {
  switch (true) {
    case filterType === ViewValues.Sdg:
    case filterType === ViewValues.SdgTarget:
      return filterSdg(question, blueprint, filterValue)
    case filterType === ViewValues.StandardsAndFrameworks:
    case filterType === ViewValues.Regulatory:
    case filterType === ViewValues.Ratings:
    case filterType === ViewValues.Group:
      return filterStandardAndFramework(question, filterValue);
    case filterType === ViewValues.Standards:
      return filterStandard(question, filterValue)
    case filterType === ViewValues.Frameworks:
      return filterFramework(question, filterValue)
    case Object.values(Materiality).includes(filterValue as Materiality):
      return filterMateriality(question, filterValue as Materiality, materiality, blueprint)
    case filterType === ViewValues.Users:
      return filterUsers(question, filterValue)
    case filterType === ViewValues.Roles:
      return filterQuestionsByRoles(question, filterValue)
    case filterType === ViewValues.QuestionPacks:
    case filterType === ViewValues.AllPacks:
      return filterQuestionPacks(question, filterValue, metricGroups)
    case filterType === ViewValues.Custom:
    default:
      return filterMetricGroups({
        utrId: question.universalTracker.getId(),
        metricGroupId: filterValue,
        metricGroups,
      });
  }
}

export type BreadcrumbFilter = { type: string, value?: string };

// sdg/8/8.2/un_gc |
export const convertToFilters = (view: ViewValues, breadcrumbs: Breadcrumb[]) => {

  const filters: BreadcrumbFilter[] = [];

  let filter: BreadcrumbFilter = { type: view };
  for (const { cardCategory: filterValue, cardGroup: filterBy } of breadcrumbs) {
    filter.value = filterValue;
    filters.push(filter);
    // Reset to new
    filter = { type: filterBy };
  }

  return filters;
}

const isScopeQuestionList = (isScopePage: boolean, breadcrumbs: Breadcrumb[]) => {
  if (!isScopePage) {
    return false;
  }

  const lastFilter = breadcrumbs[breadcrumbs.length - 1];
  return lastFilter?.cardGroup === ViewValues.QuestionList || lastFilter?.cardGroup === ViewValues.StandardsQuestionList;
};

export const useScopeFilters = (props: {
  page: string,
  view: ViewValues,
  breadcrumbs: Breadcrumb[],
  blueprint: BlueprintContributions | undefined,
  metricGroups: MetricGroup[],
  materiality?: MaterialityObject,
  originalQuestionList: Map<string, ScopeQuestion>,
  baseScopeQuestion: ScopeQuestionOptionalValue[]
}) => {
  const {
    page,
    view,
    breadcrumbs,
    blueprint,
    metricGroups,
    materiality,
    originalQuestionList,
    baseScopeQuestion
  } = props;

  return useMemo(() => {
    if (!blueprint) {
      return [];
    }

    const isScopePage = page === 'scope';
    const questions = isScopePage ? baseScopeQuestion : originalQuestionList.values();

    const materialityMap = getMaterialityMap(materiality)
    const filters = convertToFilters(view, breadcrumbs);
    const questionList: (ScopeQuestion | ScopeQuestionOptionalValue)[] = [];
    const allMetricGroups = flattenBySubgroups(metricGroups);

    for (const q of questions) {
      const match = filters.every(({ type, value }) => {
        if (!value) {
          return false;
        }

        return filterByType({
          question: q,
          filterType: type,
          filterValue: value,
          materiality: materialityMap,
          blueprint,
          metricGroups: allMetricGroups,
        });
      });

      if (!match) {
        continue // Did not match filters, go next
      }

      if (isScopeQuestionList(isScopePage, breadcrumbs)) {
        // Try to use question with utrv data if possible in questionList
        const question = originalQuestionList.get(q.universalTracker.getCode());
        if (question) {
          questionList.push(question)
          continue;
        }
      }
      questionList.push(q);
    }

    return questionList;

  }, [
    originalQuestionList,
    baseScopeQuestion,
    page,
    view,
    breadcrumbs,
    blueprint,
    materiality,
    metricGroups
  ]);
}
