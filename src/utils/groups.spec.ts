import { describe, it, expect } from 'vitest';
import { findChildGroupByCode, flattenBySubgroups, getGroupChildrenTags } from './groups';
import { MetricGroup } from '../types/metricGroup';
import { faker } from '@faker-js/faker';
import { getGroup, getGroupChildrenTags as getGroupChildrenTagsCore } from '@g17eco/core';

describe('groups utils', () => {
  const createMetricGroup = (overrides: Partial<MetricGroup>): MetricGroup => ({
    _id: faker.database.mongodbObjectId(),
    groupName: 'Metric Group',
    initiativeId: 'initiative-id',
    updated: '2024-01-01',
    subgroups: [],
    ...overrides,
  });

  const [groupIdOne, childGroupIdOne, childGroupIdTwo, grandChildGroupIdOne, grandChildGroupIdTwo, groupIdTwo] =
    Array.from({ length: 6 }).map(() => faker.database.mongodbObjectId());

  const metricGroupOne = createMetricGroup({
    _id: groupIdOne,
    groupName: 'Group 1',
    subgroups: [
      createMetricGroup({
        _id: childGroupIdOne,
        groupName: 'Child Group 1.1',
        subgroups: [
          createMetricGroup({ _id: grandChildGroupIdOne, groupName: 'Grand Child Group 1.1.1' }),
          createMetricGroup({ _id: grandChildGroupIdTwo, groupName: 'Grand Child Group 1.1.2' }),
        ],
      }),
      createMetricGroup({ _id: childGroupIdTwo, groupName: 'Child Group 1.2' }),
    ],
  });

  const metricGroupTwo = createMetricGroup({ _id: groupIdTwo, groupName: 'Group 2' });

  describe('flattenBySubgroups', () => {
    const createFlatMetricGroup = (overrides: Partial<MetricGroup> = {}) => ({
      _id: faker.database.mongodbObjectId(),
      groupName: 'Metric Group',
      initiativeId: 'initiative-id',
      updated: '2024-01-01',
      ...overrides,
      subgroups: undefined,
    });

    it('should handle empty array', () => {
      const result = flattenBySubgroups([]);
      expect(result).toEqual([]);
    });

    it('should handle groups with no subgroups', () => {
      const metricGroups: MetricGroup[] = [
        { ...metricGroupOne, subgroups: [] },
        { ...metricGroupTwo, subgroups: [] },
      ];

      const expected = [createFlatMetricGroup(metricGroupOne), createFlatMetricGroup(metricGroupTwo)];

      const result = flattenBySubgroups(metricGroups);
      expect(result).toEqual(expected);
    });

    it('should flatten the metric groups by subgroups', () => {
      const metricGroups: MetricGroup[] = [metricGroupOne, metricGroupTwo];

      const expected = [
        createFlatMetricGroup(metricGroupOne),
        createFlatMetricGroup(metricGroupTwo),
        createFlatMetricGroup(metricGroupOne.subgroups?.[0]),
        createFlatMetricGroup(metricGroupOne.subgroups?.[1]),
        createFlatMetricGroup(metricGroupOne.subgroups?.[0].subgroups?.[0]),
        createFlatMetricGroup(metricGroupOne.subgroups?.[0].subgroups?.[1]),
      ];

      const result = flattenBySubgroups(metricGroups);
      expect(result).toEqual(expected);
    });
  });

  describe('findChildGroupByCode ', () => {
    describe('standards-and-frameworks', () => {
      it('should find child group by code - first subgroup level', () => {
        const groupCode = 'gri';
        const subGroupCode = 'gri-1';
        const group = getGroup('standards-and-frameworks', groupCode);
        if (group) {
          expect(findChildGroupByCode(group, subGroupCode)?.name).toEqual('GRI 100: Universal Standards');
        }
      });
      it('should find child group by code - second subgroup level', () => {
        const groupCode = 'gri';
        const subGroupCode = 'gri-1-102';
        const group = getGroup('standards-and-frameworks', groupCode);
        if (group) {
          expect(findChildGroupByCode(group, subGroupCode)?.name).toEqual('GRI 102: General Disclosure');
        }
      });
    });

    describe('metric-groups', () => {
      it('should return first level subgroup when find by first subgroup level', () => {
        const found = findChildGroupByCode(metricGroupOne, childGroupIdOne);
        expect(found?._id).toEqual(childGroupIdOne);
        expect(found?.groupName).toEqual('Child Group 1.1');
      });
      it('should return second level subgroup when find by second subgroup level', () => {
        const found = findChildGroupByCode(metricGroupOne, grandChildGroupIdOne);
        expect(found?._id).toEqual(grandChildGroupIdOne);
        expect(found?.groupName).toEqual('Grand Child Group 1.1.1');
      });
    });
  });

  describe('getGroupChildrenTags', () => {
    describe('standards-and-frameworks', () => {
      it('should return all children tags', () => {
        const groupCode = 'gri';
        const group = getGroup('standards-and-frameworks', groupCode);
        if (group) {
          const expectedTags = getGroupChildrenTagsCore(group);
          getGroupChildrenTags(group).forEach((tag) => {
            expect(expectedTags).toContain(tag);
          });
        }
      });
    });
    describe('metric-groups', () => {
      it('should return all children tags', () => {
        const expectedTags = [childGroupIdOne, childGroupIdTwo, grandChildGroupIdOne, grandChildGroupIdTwo];
        expect(getGroupChildrenTags(metricGroupOne)).toEqual(expectedTags);
      });
    });
  });
});
